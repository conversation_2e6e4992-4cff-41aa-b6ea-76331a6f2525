using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Avaturn.Core.Runtime.Scripts.Mobile;
using Avaturn.Core.Runtime.Scripts.Avatar;
using DG.Tweening;
//using UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets;
using FishNet.Object;
using Metaverse;
using UnityEngine.XR.Interaction.Toolkit.Locomotion.Turning;
using UnityEngine.XR.Interaction.Toolkit.Locomotion;
using UnityEngine.Animations.Rigging;
using StarterAssets;
using UnityEngine.XR.Interaction.Toolkit.Locomotion.Movement;
using UnityEngine.XR.Interaction.Toolkit;

public enum PlayerState
{
    Standing,
    Seating,
    Walking,
    Running,
    Teleporting
}

public class PlayerController : NetworkBehaviour
{

    [Header("Player Manager Componenets")]
    public PlayerMovemetManager playerMovemetManager;
    public VR_Rig_Manager vr_Rig_Manager;
    public ChipSelectionManager playerChipSelectionManager;

    [Header("Player Body General Refrences")]
    public GameObject playerAvatarBodyRoot;
    public AvaturnIframeControllerMobile avaturnIframeControllerMobile;
    public AvatarReceiver avatarReceiver;
    public Camera mainPlayerCamera;

    //public Avaturn.Samples.Runtime._Data.Plugins.Third_Person_Controller.Scripts.ThirdPersonController thirdPersonController;
    public ThirdPersonController thirdPersonController;
    public CharacterController characterController;
    //public DynamicMoveProvider dynamicMoveProvider;
    public GameObject dynamicMoveProvider;
    public Transform currentTeleportPoint;


    public PlayerState currentState;

    [Header("Seating State Config")]
    public float _cc_SeatingHeight;
    public float _cc_Seating_YOffset;

    [Header("Standing State Config")]
    public float _cc_StandingHeight;
    public float _cc_Standing_YOffset;

    public GameObject Popup;
    public string GameName;

    public Transform cameraOffset;

    public GameObject LoadingScreen;

    [Header("VR Platform Component Variables")]
    public Unity.XR.CoreUtils.XROrigin XROrigin;
    public UnityEngine.XR.Interaction.Toolkit.Inputs.InputActionManager inputActionManager;
    //public LocomotionSystem locomotionSystem;
    public LocomotionMediator locomotionMediator;
    //public CharacterControllerDriver characterControllerDriver;
    //public ActionBasedContinuousTurnProvider actionBasedContinuousTurnProvider;
    public ContinuousTurnProvider continuousTurnProvider;
    //public ActionBasedSnapTurnProvider actionBasedSnapTurnProvider;
    //public SnapTurnProvider snapTurnProvider;
    public UnityEngine.XR.Interaction.Toolkit.Locomotion.Teleportation.TeleportationProvider teleportationProvider;

    // Add these fields to store original values for VR mode
    public Vector3 originalPosition;
    public Quaternion originalRotation;
    public Vector3 originalHeadOffset = Vector3.zero;

    public GameObject nearFarObject, rightHandFinger, rightHand,PokeInteractor;

    public UnityEngine.XR.Interaction.Toolkit.Interactors.XRPokeInteractor rayInteractor;

public Animator animator;
    public override void OnStartClient()
    {
        base.OnStartClient();
        if (base.IsOwner)
        {
            thirdPersonController.enabled = false;
            setPlayer();
        }
        else
        {
            thirdPersonController.enabled = false;
            mainPlayerCamera.enabled = false;
        }
    }

    public RuntimeAnimatorController playerController;
    public RigBuilder playerRigBuilder;

    public Rig playerRig;
    public VR_IK_ConstraintsManager vR_IK_ConstraintsManager;

    public RigBuilder playerChild;

    public GameObject FPSView, TPSView, FPSViewRoullete;

    private void Start()
    {
        currentState = PlayerState.Standing;
        if (false) //old GameConfigManager.Instance.isEditorTest
        {
            XROrigin.enabled = false;
            inputActionManager.enabled = false;
            //locomotionSystem.enabled = false;
            locomotionMediator.enabled = false;
            //dynamicMoveProvider.enabled = false;
            //characterControllerDriver.enabled = false\
            continuousTurnProvider.enabled = false;
            //snapTurnProvider.enabled = false;
            teleportationProvider.enabled = false;
            vr_Rig_Manager.GetComponent<Animator>().runtimeAnimatorController = playerController;
            playerChild.GetComponent<Animator>().runtimeAnimatorController = playerController;

            vr_Rig_Manager.headIK_BodyPosOffset.y = 0f;
            playerChild.Build();

            playerRigBuilder.Build();

            vr_Rig_Manager.enabled = false;
            playerRigBuilder.enabled = false;
            playerRig.enabled = false;
            vR_IK_ConstraintsManager.enabled = false;
        }
    }


    void setPlayer()
    {
        try
        {
            MainManager.Instance.localPlayerRigRoot = this.gameObject;
            MainManager.Instance.localPayerController = this.transform.GetComponent<PlayerController>();
        }
        catch { }
        try
        {
            MainManager.Instance.localPlayerAvatarRoot = this.vr_Rig_Manager.gameObject;
        }catch { }
        //        MainManager.Instance.dynamicMoveProvider = this.dynamicMoveProvider;
        thirdPersonController.enabled = true;
    }

    private void OnTriggerEnter(Collider other)
    {
        //   Debug.Log("Collided Object namee ====> " + other.gameObject.name + " || Has Tag ====> " + other.gameObject.tag);

        if (other.CompareTag("TeleportationPoint"))
        {
            StartCoroutine(TeleportCoroutine(other.gameObject));
        }
        else if (other.CompareTag("HitBtn"))
        {
            Debug.Log("Hit Btn");
            other.transform.DOMoveY(-0.07f, 0.2f).OnComplete(() =>
            {
                other.transform.DOMoveY(0, 0.2f);
            });
        }
        else if (other.CompareTag("StandBtn"))
        {
            Debug.Log("Stand Btn");
        }
    }


    public IEnumerator TeleportCoroutine(GameObject collisionGO)
    {

        PassThroughTeleportationManager passThroughTeleportationManager = collisionGO.GetComponent<PassThroughTeleportationManager>();

        //thirdPersonController.isTeleporting = true;
        currentTeleportPoint = passThroughTeleportationManager.PassThroughTeleport(gameObject, currentTeleportPoint); //other.transform.GetComponent<PassThroughTeleportationManager>().PassThroughTeleport(this.gameObject, currentTeleportPoint);

        yield return new WaitForSeconds(passThroughTeleportationManager.transitionPanel.transitionHideInvokeTimer);

        //thirdPersonController.isTeleporting = false;
    }

    // Method to change the player's state
    public void ChangeState(PlayerState newState)
    {
        switch (newState)
        {
            case PlayerState.Standing:
                HandleStanding();
                break;

            case PlayerState.Seating:
                HandleSeating();
                break;

            case PlayerState.Walking:
                HandleWalking();
                break;

            case PlayerState.Running:
                HandleRunning();
                break;

            case PlayerState.Teleporting:
                HandleTeleporting();
                break;

            default:
                Debug.LogWarning("Unhandled state: " + newState);
                break;
        }

        // Update the current state
        currentState = newState;
        Debug.Log("Player state changed to: " + currentState);
    }

    // Define individual state handlers
    private void HandleStanding()
    {
        Debug.Log("Player is now standing.");
        canSeat = false;
        thirdPersonController.enabled = true;
        if (!GameConfigManager.Instance.isEditorTest)
            MainManager.Instance.localPlayerRigRoot.GetComponent<ContinuousMoveProvider>().enabled = true;
        //dynamicMoveProvider.enabled = true;
        Debug.Log("Third Person Controller State ====> " + thirdPersonController.isActiveAndEnabled);

        if (GameConfigManager.Instance.isEditorTest)
        {
            characterController.center = new Vector3(0, 0.93f, 0);
        }
        else
        {
            characterController.height = _cc_StandingHeight;
            characterController.center = new Vector3(characterController.center.x,
                _cc_Standing_YOffset, characterController.center.z);
                XROrigin.CameraYOffset = 1.8f;
        }

        // Re-enable rotation when standing
        if (continuousTurnProvider != null)
        {
            continuousTurnProvider.enabled = true;
            this.GetComponent<ContinuousTurnProvider>().enabled = true;
        }
        animator.SetLayerWeight(5, 1f);
    }

    private void HandleSeating()
    {
        Debug.Log("Player is now seated.");
        if (!GameConfigManager.Instance.isEditorTest)
        {
            thirdPersonController.enabled = false;
            //dynamicMoveProvider.enabled = false;
        }
        Debug.Log("Third Person Controller State ====> " + thirdPersonController.isActiveAndEnabled);

        if (GameConfigManager.Instance.isEditorTest)
        {
            thirdPersonController.enabled = false;
            characterController.center = new Vector3(0, 1.65f, 0);
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
        else
        {
            characterController.height = _cc_SeatingHeight;
            characterController.center = new Vector3(characterController.center.x,
                _cc_Seating_YOffset, characterController.center.z);
            XROrigin.CameraYOffset = 1.4f;
        }

        // Disable rotation when seated
        if (continuousTurnProvider != null)
        {
            continuousTurnProvider.enabled = false;
            this.GetComponent<ContinuousTurnProvider>().enabled = false;
        }
        animator.SetLayerWeight(5, 0f);
    }

    private void HandleWalking()
    {
        Debug.Log("Player is now walking.");
        // Add logic for walking
    }

    private void HandleRunning()
    {
        Debug.Log("Player is now running.");
        // Add logic for running
    }

    private void HandleTeleporting()
    {
        Debug.Log("Player is teleporting.");
        // Add logic for teleporting
    }

    public void PlayGameNoBtnClick()
    {
        Popup.gameObject.SetActive(false);
    }

    public void PlayGameYesBtnClick()
    {
        PlayerController player = MainManager.Instance.localPlayerRigRoot.GetComponent<PlayerController>();
        Debug.Log("Trigger For Game:" + GameName);
        switch (GameName)
        {
            case "Blackjack":
                Debug.Log("Seat Hear for BlackJack");
                Blackjack_GameManager._Instance.SeatForStandaloneGame();
                break;

            case "Roulette":
                Debug.Log("Seat Hear for Roulette");
                GameSeatingManager.inst.SeatForStandaloneGame();
                break;
            case "Egyption":
                Cursor.lockState = CursorLockMode.None;
                Cursor.visible = true;
                //PlayerController player = MainManager.Instance.localPlayerRigRoot.GetComponent<PlayerController>();
                player.transform.SetParent(GameObject.Find("All Object Parent").transform.GetChild(0).transform);
                GameObject.Find("All Object Parent").transform.GetChild(0).gameObject.SetActive(false);
                GameObject.Find("Egyption Standalone").transform.GetChild(0).gameObject.SetActive(true);
                
                break;
            case "Cappadocia":
                Cursor.lockState = CursorLockMode.None;
                Cursor.visible = true;
                
                player.transform.SetParent(GameObject.Find("All Object Parent").transform.GetChild(0).transform);
                GameObject.Find("All Object Parent").transform.GetChild(0).gameObject.SetActive(false);
                GameObject.Find("Egyption Standalone").transform.GetChild(1).gameObject.SetActive(true);
                break;
            default:
                break;
        }
        Popup.gameObject.SetActive(false);
    }
    internal bool canSeat = false;
    private void Update()
    {
        if (Input.GetKeyDown(KeyCode.Y) && Popup.activeSelf && !canSeat)
        {
            canSeat = true;
            PlayGameYesBtnClick();
        }
        try
        {
            nearFarObject.transform.position = rightHandFinger.transform.position;
            PokeInteractor.transform.position = rightHandFinger.transform.position;

            // To this:
            Quaternion handRotation = rightHand.transform.rotation;
            Vector3 eulerAngles = handRotation.eulerAngles;
            //nearFarObject.transform.rotation = Quaternion.Euler(0, eulerAngles.y, eulerAngles.z);
        }catch (System.Exception)
        {
        }
    }
}
