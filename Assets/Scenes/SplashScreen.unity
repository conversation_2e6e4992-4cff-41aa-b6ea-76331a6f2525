%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 1
    m_PVRFilteringGaussRadiusAO: 1
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 20201, guid: 0000000000000000f000000000000000, type: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &82674937
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 82674938}
  - component: {fileID: 82674940}
  - component: {fileID: 82674939}
  m_Layer: 5
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &82674938
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 82674937}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 807757972}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 500, y: 500}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &82674939
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 82674937}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 60762a6c3c9724f98b643d878b3a2fe6, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &82674940
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 82674937}
  m_CullTransparentMesh: 1
--- !u!1 &128463933
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 128463936}
  - component: {fileID: 128463935}
  - component: {fileID: 128463934}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &128463934
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 128463933}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01614664b831546d2ae94a42149d80ac, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_MoveRepeatDelay: 0.5
  m_MoveRepeatRate: 0.1
  m_XRTrackingOrigin: {fileID: 0}
  m_ActionsAsset: {fileID: -944628639613478452, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_PointAction: {fileID: -1654692200621890270, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_MoveAction: {fileID: -8784545083839296357, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_SubmitAction: {fileID: 392368643174621059, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_CancelAction: {fileID: 7727032971491509709, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_LeftClickAction: {fileID: 3001919216989983466, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_MiddleClickAction: {fileID: -2185481485913320682, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_RightClickAction: {fileID: -4090225696740746782, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_ScrollWheelAction: {fileID: 6240969308177333660, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_TrackedDevicePositionAction: {fileID: 6564999863303420839, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_TrackedDeviceOrientationAction: {fileID: 7970375526676320489, guid: ca9f5fa95ffab41fb9a615ab714db018,
    type: 3}
  m_DeselectOnBackgroundClick: 1
  m_PointerBehavior: 0
  m_CursorLockBehavior: 0
  m_ScrollDeltaPerTick: 6
--- !u!114 &128463935
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 128463933}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &128463936
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 128463933}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &747373505
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 747373507}
  - component: {fileID: 747373506}
  - component: {fileID: 747373508}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &747373506
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747373505}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 0.95686275, b: 0.8392157, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &747373507
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747373505}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!114 &747373508
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747373505}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &807757967
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 807757972}
  - component: {fileID: 807757971}
  - component: {fileID: 807757970}
  - component: {fileID: 807757969}
  - component: {fileID: 807757968}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &807757968
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 807757967}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 265ab3bed53b64e96b28f5c1a14db5ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _logoImg: {fileID: 82674938}
  _mainScene: MenuScene
  _splashAnimationDuration: 6
--- !u!114 &807757969
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 807757967}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &807757970
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 807757967}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!223 &807757971
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 807757967}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!224 &807757972
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 807757967}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1033617972}
  - {fileID: 82674938}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 1920, y: 1080}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1010282836
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1010282838}
  - component: {fileID: 1010282837}
  m_Layer: 0
  m_Name: Audio Source
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!82 &1010282837
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1010282836}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: 865e774a6078a421da73829f5b84c423, type: 3}
  m_Resource: {fileID: 8300000, guid: 865e774a6078a421da73829f5b84c423, type: 3}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!4 &1010282838
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1010282836}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 3.5670319, y: -0.8485234, z: 31.55131}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1033617971
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1033617972}
  - component: {fileID: 1033617974}
  - component: {fileID: 1033617973}
  m_Layer: 5
  m_Name: BG
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1033617972
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033617971}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 807757972}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1033617973
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033617971}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1033617974
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033617971}
  m_CullTransparentMesh: 1
--- !u!1 &35816823359292901
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 559369952382032964}
  m_Layer: 0
  m_Name: Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &51034587480286600
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2829692256499286248}
  m_Mesh: {fileID: 22788929071467060, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!114 &57552291714031914
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2083018880704276259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 36
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 0
    m_MaxSize: 56
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: New Text
--- !u!1 &70538000784449429
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3086063312870280375}
  m_Layer: 0
  m_Name: Fill Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &71643286533970796
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4409373711675685735}
  - component: {fileID: 7244796730452608866}
  - component: {fileID: 2745325864379839978}
  - component: {fileID: 5499352854522851950}
  m_Layer: 0
  m_Name: Button (Legacy)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &118680253955205976
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7082462151077418800}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5079801, y: -0.5056839, z: 0.49523896, w: 0.49089563}
  m_LocalPosition: {x: -0.7104416, y: 1.5039446, z: -0.04624605}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7992904332792918524}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!224 &119037776445706993
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2083018880704276259}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7095667580457311747}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -4.6, y: -74.5}
  m_SizeDelta: {x: 300, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &134444605805926517
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2420031529279389316}
  - component: {fileID: 361403495987376044}
  - component: {fileID: 7852162772088412140}
  - component: {fileID: 2354001825908910687}
  m_Layer: 0
  m_Name: NearFar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &143614111516700755
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7286427651859038901}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c2fadf230d1919748a9aa21d40f74619, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackingType: 0
  m_UpdateType: 0
  m_IgnoreTrackingState: 0
  m_PositionInput:
    m_UseReference: 1
    m_Action:
      m_Name: Position
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: e0974d43-a211-4251-882f-3f0b4749db16
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 3220680263695665919, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_RotationInput:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 0a88b07c-2048-460b-b52d-880dd98ceb35
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -5930349909990434036, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_TrackingStateInput:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: ca2485cb-f4d4-4bef-84e6-b085e080175c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 2069149553511882089, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_PositionAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: b902054d-edbb-440a-a455-68558ef17b58
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  m_RotationAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 629180f1-a17a-4e47-a583-481808df540f
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
--- !u!114 &150833435104077431
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766749749359418194}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 67db9e8f0e2ae9c40bc1e2b64352a6b4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 3954076349469409366}
  m_FillRect: {fileID: 6137808655463954828}
  m_HandleRect: {fileID: 3304550752943941888}
  m_Direction: 0
  m_MinValue: 0
  m_MaxValue: 1
  m_WholeNumbers: 0
  m_Value: 0
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &175434157854035718
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1270683876034729651}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 64d299502104b064388841ec2adf6def, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Target: {fileID: 9031794777427850043}
  m_AimTargetObject: {fileID: 0}
  m_UseLocalSpace: 1
  m_AngleStabilization: 20
  m_PositionStabilization: 0.25
--- !u!224 &194626214125372492
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4432447139325270252}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2207550626579633132}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 1}
--- !u!1 &205920812168309993
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6223613710194887899}
  m_Layer: 0
  m_Name: Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &260707104338999143
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169109927468303437}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 792f6c7eaa1a4b82abf8351559ac97eb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransformToFollow: {fileID: 7265973089823045129}
  m_MotionStabilizationMode: 1
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_SmoothOffset: 0
  m_SmoothingSpeed: 10
  m_UseDistanceBasedVelocityScaling: 1
  m_UseMomentum: 1
  m_MomentumDecayScale: 1.25
  m_ZVelocityRampThreshold: 0.3
  m_PullVelocityBias: 1
  m_PushVelocityBias: 1.25
  m_MinAdditionalVelocityScalar: 0.05
  m_MaxAdditionalVelocityScalar: 1.5
  m_EnableDebugLines: 0
--- !u!114 &261570902888713761
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6915321513614555395}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!4 &279653332813315513
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7141565287705451588}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0010705729, y: -0.0007987698, z: -0.010620061, w: 0.9999427}
  m_LocalPosition: {x: -0.0000000041884216, y: 0.25669503, z: -0.00000023872377}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5777818085006005823}
  - {fileID: 5911338096449038408}
  - {fileID: 5222654588366059119}
  - {fileID: 1460506307876685992}
  - {fileID: 4400452983571757765}
  - {fileID: 5668317968300891774}
  m_Father: {fileID: 8517523847440643472}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &291898865573910355
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2510119848456018298}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8b94c4c83dec6a94fbaebf543478259e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 7739599882840324738}
  m_TransformationPriority: 0
  m_EnableFreeXMovement: 1
  m_EnableFreeYMovement: 0
  m_EnableFreeZMovement: 1
  m_UseGravity: 1
  m_GravityApplicationMode: 0
  m_ControllerTransform: {fileID: 6706157922200873912}
  m_EnableMoveWhileSelecting: 0
  m_MoveFactor: 1
  m_GrabMoveInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Grab Move
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 2e9a23ce-d949-4c67-9b12-7a9a35510733
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Grab Move Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 3680a95b-119c-4eba-b8fe-7e0a362e460b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: -3742484312079769484, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: -3742484312079769484, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_GrabMoveAction:
    m_UseReference: 1
    m_Action:
      m_Name: Grab Move
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: 3d33edcf-0043-45cb-95a7-008204badf83
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
--- !u!224 &293992037062236184
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7843615889005886009}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.0005, y: 0.0005, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7730492227343333247}
  m_Father: {fileID: 4285635575700484274}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 2.28}
  m_SizeDelta: {x: 1920, y: 1080}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!135 &296868951552395432
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1072080526943758045}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.05
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &317081877040910432
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5666268692752496191}
  m_Layer: 0
  m_Name: LeftHandRing1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &318914851436607795
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7350190245303620506}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3827930346453364790}
  - {fileID: 5990502202272607132}
  - {fileID: 5741279186860494031}
  - {fileID: 7052516765373991646}
  - {fileID: 1796092491595218133}
  - {fileID: 2177334080802346132}
  m_Father: {fileID: 3586357643331768374}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &332667422860186626
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7196990757102031841}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5432997, y: -0.011283805, z: -0.006971506, w: 0.83943415}
  m_LocalPosition: {x: 0.000000004332459, y: 0.4308915, z: 0.0000000030731877}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8974198899552680212}
  m_Father: {fileID: 5061350368787562714}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &350673018438696253
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7614375382012231164}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &361403495987376044
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 134444605805926517}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c83f12c537584f51b92c01f10d7090c0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransitionDuration: 0.125
  m_InteractorSource: {fileID: 7812155368381269535}
  m_IgnoreHoverEvents: 0
  m_IgnoreSelectEvents: 0
  m_IgnoreActivateEvents: 1
  m_IgnoreUGUIHover: 0
  m_IgnoreUGUISelect: 0
  m_IgnoreXRInteractionEvents: 0
  m_SelectClickAnimationMode: 1
  m_ActivateClickAnimationMode: 1
  m_ClickAnimationDuration: 0.25
  m_ClickAnimationCurve:
    m_UseConstant: 1
    m_ConstantValue:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_Variable: {fileID: 0}
--- !u!114 &365169162618499662
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5672919205124789578}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c28f071e86c466d45a6f2e409044713a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Animator: {fileID: 2307306175086723115}
  VR_IK_ConstraintsManager: {fileID: 6090362364418762880}
  targetHumanoidAvtar: {fileID: 0}
--- !u!4 &375204505576286140
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4635425379091036855}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7851588616201146884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &378383395634389564
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1362816434783236798}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 95a8098103f299b44be14d0de50e7fe3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  isMovingForward: 0
  terrainLayer:
    serializedVersion: 2
    m_Bits: 1
  body: {fileID: 4285635575700484274}
  otherFoot: {fileID: 2668930533464041367}
  speed: 4
  stepDistance: 0.2
  stepLength: 0.2
  sideStepLength: 0.1
  stepHeight: 0.3
  footOffset: {x: 0, y: 0, z: 0}
  footRotOffset: {x: -120, y: 180, z: 0}
  footYPosOffset: 0.1
  rayStartYOffset: 0
  rayLength: 1.5
--- !u!114 &409048068601817664
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5610636252869755943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61a144f4019577a4684e3c370e321322, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animationInputs:
  - animationPropertyName: Right Pinch
    action:
      m_UseReference: 1
      m_Action:
        m_Name: Action
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: d81c41fe-8e9a-4402-8385-39ce31bb1cb7
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_Reference: {fileID: 7904272356298805229, guid: c348712bda248c246b8c49b3db54643f,
        type: 3}
  - animationPropertyName: Right Grab
    action:
      m_UseReference: 1
      m_Action:
        m_Name: Action
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: 87d0eba2-f005-4010-ad43-db14e9083f16
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_Reference: {fileID: -1758520528963094988, guid: c348712bda248c246b8c49b3db54643f,
        type: 3}
  - animationPropertyName: Left Pinch
    action:
      m_UseReference: 1
      m_Action:
        m_Name: Action
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: c49455cc-f2bb-4c40-b26f-8fb57b813bbe
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_Reference: {fileID: -4289430672226363583, guid: c348712bda248c246b8c49b3db54643f,
        type: 3}
  - animationPropertyName: Left Grab
    action:
      m_UseReference: 1
      m_Action:
        m_Name: Action
        m_Type: 0
        m_ExpectedControlType: 
        m_Id: ef5c96bb-bf4f-4eaa-99d3-7365d6a42cb8
        m_Processors: 
        m_Interactions: 
        m_SingletonActionBindings: []
        m_Flags: 0
      m_Reference: {fileID: 6558622148059887818, guid: c348712bda248c246b8c49b3db54643f,
        type: 3}
  moveInput:
    m_UseReference: 0
    m_Action:
      m_Name: 
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: aa04f501-146a-4b24-9dcf-7d095d44dd7e
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
  animator: {fileID: 2307306175086723115}
--- !u!114 &431838966104137747
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7722944017648515500}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aeda7bfbf984f2a4da5ab4b8967b115d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Weight: 1
  m_Data:
    m_Root: {fileID: 7334807809387987209}
    m_Mid: {fileID: 8517523847440643472}
    m_Tip: {fileID: 279653332813315513}
    m_Target: {fileID: 3806786343414362346}
    m_Hint: {fileID: 2238439274622436440}
    m_TargetPositionWeight: 1
    m_TargetRotationWeight: 1
    m_HintWeight: 1
    m_MaintainTargetPositionOffset: 0
    m_MaintainTargetRotationOffset: 0
--- !u!1 &433086827229989035
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2808095484452820845}
  m_Layer: 0
  m_Name: LeftHandIndex3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &433312847804040521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6706157922200873912}
  - component: {fileID: 1436958524581276553}
  - component: {fileID: 1006773482236885328}
  - component: {fileID: 1107543382018816462}
  - component: {fileID: 4610631522096466495}
  - component: {fileID: 2184974203031281454}
  - component: {fileID: 4167201772481382799}
  - component: {fileID: 1165540589783183136}
  m_Layer: 0
  m_Name: Left Controller
  m_TagString: LeftHand
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &453748873995594232
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c9b3d17eeb2e6bc47ada81d8f7f638d8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_GazeInteractor: {fileID: 541500167590247934}
  m_FallbackDivergence: 60
  m_HideCursorWithNoActiveRays: 1
  m_RayInteractors:
  - m_Interactor: {fileID: 0}
    m_TeleportRay: 0
  - m_Interactor: {fileID: 1062335248880780195}
    m_TeleportRay: 1
  - m_Interactor: {fileID: 0}
    m_TeleportRay: 0
  - m_Interactor: {fileID: 4509087775236993182}
    m_TeleportRay: 1
  m_AimAssistRequiredAngle: 30
  m_AimAssistRequiredSpeed: 0.25
  m_AimAssistPercent: 0.8
  m_AimAssistMaxSpeedPercent: 10
--- !u!4 &475444165889946360
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6536985365452920497}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4683310073714609297}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &484607128982752228
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8169210416909721770}
  - component: {fileID: 4511218935134170365}
  - component: {fileID: 2694127015239756176}
  - component: {fileID: 1959180444716697021}
  m_Layer: 0
  m_Name: LineVisual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &496353250390962969
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1514165820928196898}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.36144, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8695576789228953066}
  - {fileID: 9031794777427850043}
  - {fileID: 4248990946545048892}
  - {fileID: 6706157922200873912}
  - {fileID: 2005964267215810734}
  - {fileID: 545667201402663227}
  - {fileID: 6291295526486202380}
  m_Father: {fileID: 3797029478118711239}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &535261126972462885
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1394576125987870698}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4248990946545048892}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &541500167590247934
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7286427651859038901}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c416f1a5c494e224fb5564fd1362b50d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 1
  m_Handedness: 0
  m_AttachTransform: {fileID: 535261126972462885}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 26dcb486-2cd5-4bf0-83a4-8252a6419ca1
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: d589f510-c88e-41dc-89ee-4accd74ded87
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 1b75ac5e-63d2-4c5c-9b86-0fe382e6b137
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: fde38f1d-c7ff-4233-b8ba-2548488943d7
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 0
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 0
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 0
  m_LineType: 0
  m_BlendVisualLinePoints: 1
  m_MaxRaycastDistance: 30
  m_RayOriginTransform: {fileID: 4248990946545048892}
  m_ReferenceFrame: {fileID: 0}
  m_Velocity: 16
  m_Acceleration: 9.8
  m_AdditionalGroundHeight: 0.1
  m_AdditionalFlightTime: 0.5
  m_EndPointDistance: 30
  m_EndPointHeight: -10
  m_ControlPointDistance: 10
  m_ControlPointHeight: 5
  m_SampleFrequency: 20
  m_HitDetectionType: 0
  m_SphereCastRadius: 0.1
  m_ConeCastAngle: 6
  m_LiveConeCastDebugVisuals: 0
  m_RaycastMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RaycastTriggerInteraction: 1
  m_RaycastSnapVolumeInteraction: 0
  m_HitClosestOnly: 0
  m_HoverToSelect: 1
  m_HoverTimeToSelect: 1
  m_AutoDeselect: 1
  m_TimeToAutoDeselect: 0.25
  m_EnableUIInteraction: 1
  m_BlockInteractionsWithScreenSpaceUI: 0
  m_BlockUIOnInteractableSelection: 1
  m_ManipulateAttachTransform: 0
  m_UseForceGrab: 0
  m_RotateSpeed: 180
  m_TranslateSpeed: 1
  m_RotateReferenceFrame: {fileID: 0}
  m_RotateMode: 0
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_EnableARRaycasting: 0
  m_OccludeARHitsWith3DObjects: 0
  m_OccludeARHitsWith2DObjects: 0
  m_ScaleMode: 0
  m_UIPressInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 8e16e22c-3195-4e76-b0d2-9ec60d8bfc8e
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: f2af0d9f-965a-4778-accc-36828a6e40b8
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 83ec219e-cbbd-4b69-9013-f330cea06247
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_TranslateManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Translate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 78fd2d8b-06b9-4583-8d46-a896cee22152
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RotateManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Rotate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 2941b9e4-5f6c-48d5-9338-136ff3a60e62
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_DirectionalManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Directional Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 4decd4bd-22c4-4e97-af9b-f22d09a3ea8e
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleToggleInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 4e7a5ca5-86e4-4d9d-9678-01fb7083e39b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Scale Toggle Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 0c82215d-82dc-4f94-a5a2-cd3a7186171a
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ScaleOverTimeInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Over Time
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: ec6d1bcc-90c1-4e80-98a9-7de37426ef90
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleDistanceDeltaInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Distance Delta
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 7e170bb1-3339-4b96-bbd9-61c01cb414db
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
  m_GazeAssistanceCalculation: 1
  m_GazeAssistanceColliderFixedSize: 1
  m_GazeAssistanceColliderScale: 1
  m_GazeAssistanceSnapVolume: {fileID: 0}
  m_GazeAssistanceDistanceScaling: 0
  m_ClampGazeAssistanceDistanceScaling: 0
  m_GazeAssistanceDistanceScalingClampValue: 0
--- !u!4 &545667201402663227
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1072080526943758045}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6201949576961765177}
  - {fileID: 7265973089823045129}
  - {fileID: 1437907489910130082}
  - {fileID: 3586357643331768374}
  - {fileID: 9033221340210552336}
  m_Father: {fileID: 496353250390962969}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &545711285861446478
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6411585347646833231}
  - component: {fileID: 3674717633635777532}
  - component: {fileID: 1774017295705756523}
  - component: {fileID: 8278728292318932326}
  m_Layer: 0
  m_Name: LineVisual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &553272870018331482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6798434318218008223}
  m_Layer: 0
  m_Name: LeftHandThumb3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &559369952382032964
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 35816823359292901}
  serializedVersion: 2
  m_LocalRotation: {x: 0.2410187, y: 0.000000107509294, z: 0.000000041064638, w: 0.9705205}
  m_LocalPosition: {x: 4.7103443e-15, y: 0.18772878, z: 0.000000009590303}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6223613710194887899}
  m_Father: {fileID: 3421309572655730664}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &576178702574633366
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6536985365452920497}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1410cbaaadf84a7aaa6459d37ad21b3a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Renderer: {fileID: 2234876244449394634}
  m_MaterialIndex: 0
--- !u!224 &577758435368920075
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2557475164967223925}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2609061317010167862}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 7.199951, y: 0}
  m_SizeDelta: {x: -14.400014, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &592124764034376607
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3640138343546388506}
  - component: {fileID: 5118935179944215824}
  m_Layer: 2
  m_Name: Move
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!222 &611654447958032654
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6915321513614555395}
  m_CullTransparentMesh: 1
--- !u!4 &634068311700983852
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4156096697780834924}
  serializedVersion: 2
  m_LocalRotation: {x: 0.013443347, y: 0.0150710475, z: 0.00070792605, w: 0.9997958}
  m_LocalPosition: {x: -0.00000005121475, y: 0.037637375, z: -0.0000000840886}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2808095484452820845}
  m_Father: {fileID: 4386289235407016483}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &640092585440998972
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3374826707889493468}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 03005cac3f6f875458946d66fc12fe82, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  chips: []
  _chipManager: {fileID: 0}
--- !u!1 &675675067294835507
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6643006642981370824}
  m_Layer: 0
  m_Name: LeftHandThumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &679166823348159564
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7559781762829685010}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8636585185768493816}
  m_Father: {fileID: 7062042876505683019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 83.907}
  m_Pivot: {x: 0.5, y: 0}
--- !u!4 &692624218676177310
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8288753712382547403}
  serializedVersion: 2
  m_LocalRotation: {x: -0.006216736, y: -0.0027872517, z: 0.0037099181, w: 0.9999699}
  m_LocalPosition: {x: -0.000000040849066, y: 0.03999129, z: 0.00000006603096}
  m_LocalScale: {x: 0.9999999, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6606507566438310603}
  m_Father: {fileID: 7421115535572327446}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &712250697776636523
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2749870845763744358}
  m_Layer: 0
  m_Name: LeftHandPinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &715742585284539564
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2761774296399923716}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0043175095, y: 0.00035057546, z: -0.0378238, w: 0.999275}
  m_LocalPosition: {x: -0.000000005700112, y: 0.25240326, z: 0.000000025392971}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7577900487402064988}
  m_Father: {fileID: 1228367496959184203}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &717773650996758387
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7271366320287254483}
  m_Layer: 0
  m_Name: Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &749406897334760467
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4545920090398020782}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1410cbaaadf84a7aaa6459d37ad21b3a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Renderer: {fileID: 4278881197966947620}
  m_MaterialIndex: 0
--- !u!33 &810874885818669664
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6705022225288302881}
  m_Mesh: {fileID: 8449303727733987256, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!23 &823299636642840592
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3885301077535107034}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!23 &833644704101840033
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2829692256499286248}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 9f12d299d16099343a3c5c0d7285822a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &836149343400892075
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 883269954257450315}
  m_Layer: 0
  m_Name: RightUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &848228539067598214
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7564145236229543277}
  - component: {fileID: 6589592714198776237}
  m_Layer: 0
  m_Name: AvatarReceiver
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &867021761789850697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 62899f850307741f2a39c98a8b639597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Actions: {fileID: 0}
  m_NotificationBehavior: 0
  m_UIInputModule: {fileID: 0}
  m_DeviceLostEvent:
    m_PersistentCalls:
      m_Calls: []
  m_DeviceRegainedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ControlsChangedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ActionEvents: []
  m_NeverAutoSwitchControlSchemes: 0
  m_DefaultControlScheme: 
  m_DefaultActionMap: 
  m_SplitScreenIndex: -1
  m_Camera: {fileID: 0}
--- !u!4 &883269954257450315
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 836149343400892075}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000019470644, y: -0.0027354367, z: -0.99999624, w: -5.326092e-10}
  m_LocalPosition: {x: 0.08570601, y: -0.030165778, z: 0.00057580794}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8023833221090823321}
  m_Father: {fileID: 4975108577388796292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &895014221006676911
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6572348054715713375}
  - component: {fileID: 4757578893115389222}
  m_Layer: 0
  m_Name: Head_VR_Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &966853248386523949
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2207550626579633132}
  - component: {fileID: 7255750962920319719}
  - component: {fileID: 2089684147691075087}
  m_Layer: 5
  m_Name: CanvasWebViewPrefab
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &988006580706884662
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7461747219998478995}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aeda7bfbf984f2a4da5ab4b8967b115d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Weight: 1
  m_Data:
    m_Root: {fileID: 1228367496959184203}
    m_Mid: {fileID: 715742585284539564}
    m_Tip: {fileID: 7577900487402064988}
    m_Target: {fileID: 118680253955205976}
    m_Hint: {fileID: 7428536110082899187}
    m_TargetPositionWeight: 1
    m_TargetRotationWeight: 1
    m_HintWeight: 1
    m_MaintainTargetPositionOffset: 0
    m_MaintainTargetRotationOffset: 0
--- !u!114 &1006773482236885328
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433312847804040521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a4a50d88b55b45648927679791f472de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_GroupName: Left
  m_InteractionManager: {fileID: 0}
  m_StartingGroupMembers:
  - {fileID: 1676166793883116007}
  - {fileID: 1062557283441490215}
  m_StartingInteractionOverridesMap:
  - groupMember: {fileID: 1676166793883116007}
    overrideGroupMembers:
    - {fileID: 1062557283441490215}
--- !u!1 &1044456232784710588
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4783094969730892873}
  - component: {fileID: 8691014723773380613}
  - component: {fileID: 1678804837777675695}
  m_Layer: 0
  m_Name: Button_Home
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1062036589615467262
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7334807809387987209}
  m_Layer: 0
  m_Name: RightArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1062335248880780195
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3652913999594940763}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6803edce0201f574f923fd9d10e5b30a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 2147483648
  m_Handedness: 1
  m_AttachTransform: {fileID: 2011203541378567110}
  m_KeepSelectedTargetValid: 0
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 36843f28-4fd5-4729-b5a6-afe92ef11597
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 1a51c331-470d-4462-b8e1-2522a24bd40c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 1263111715868034790, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: 1263111715868034790, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 0ace7244-e61f-4e60-8d0b-2ef8c3ae51af
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: bdf06a24-21b3-4f27-a8a3-72086e6c7f00
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: -5982496924579745919, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: -4289430672226363583, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 0
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 0
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 0
  m_LineType: 1
  m_BlendVisualLinePoints: 1
  m_MaxRaycastDistance: 30
  m_RayOriginTransform: {fileID: 2005964267215810734}
  m_ReferenceFrame: {fileID: 0}
  m_Velocity: 10
  m_Acceleration: 9.8
  m_AdditionalGroundHeight: 0.1
  m_AdditionalFlightTime: 0.5
  m_EndPointDistance: 30
  m_EndPointHeight: -10
  m_ControlPointDistance: 10
  m_ControlPointHeight: 5
  m_SampleFrequency: 50
  m_HitDetectionType: 0
  m_SphereCastRadius: 0.1
  m_ConeCastAngle: 6
  m_LiveConeCastDebugVisuals: 0
  m_RaycastMask:
    serializedVersion: 2
    m_Bits: 2147483681
  m_RaycastTriggerInteraction: 1
  m_RaycastSnapVolumeInteraction: 1
  m_HitClosestOnly: 1
  m_HoverToSelect: 0
  m_HoverTimeToSelect: 0.5
  m_AutoDeselect: 0
  m_TimeToAutoDeselect: 1
  m_EnableUIInteraction: 0
  m_BlockInteractionsWithScreenSpaceUI: 0
  m_BlockUIOnInteractableSelection: 1
  m_ManipulateAttachTransform: 1
  m_UseForceGrab: 0
  m_RotateSpeed: 180
  m_TranslateSpeed: 0
  m_RotateReferenceFrame: {fileID: 0}
  m_RotateMode: 1
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_EnableARRaycasting: 0
  m_OccludeARHitsWith3DObjects: 0
  m_OccludeARHitsWith2DObjects: 0
  m_ScaleMode: 0
  m_UIPressInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 54867c8e-3650-4605-a53c-ee8ffb351dcf
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 257d8673-0295-4ff5-b278-e63d20cd918b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 8c6b1aac-a242-4bf4-a5b3-bfad6e83b638
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_TranslateManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Translate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 0f9fd0ee-650d-41a6-ab30-2a036c425c21
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RotateManipulationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Rotate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: cadca2d2-f642-4efc-a222-c1827be3e896
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -7363382999065477798, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_DirectionalManipulationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Directional Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 472cbca3-7add-47a9-a5fc-73d3d10107aa
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8811388872089202044, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleToggleInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 692a9304-a2fd-4dbd-9e2f-2fb4b6154f1c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Scale Toggle Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: d0cf082b-f2d7-4100-b069-651cf2820425
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ScaleOverTimeInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Over Time
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 6f835f0d-f1c2-461c-b8bc-edc587e89149
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleDistanceDeltaInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Distance Delta
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 9140e1d5-f197-46d4-88c2-a02441edeac5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
--- !u!114 &1062557283441490215
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6966779063830867726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 25a07ef133a37d140a87cdf1f1c75fdf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 1
  m_Handedness: 1
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 2491b664-3d4e-4f20-a7ae-ee1861d845f2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: fc42dacc-33eb-41ec-9c17-d242ac6b0c5b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: -6131295136447488360, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: 6558622148059887818, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 77660b9e-6bbe-4740-b80f-1fea8d0f59e1
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 94568f52-c27a-47fc-a190-5e3b17572929
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: -5982496924579745919, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: -4289430672226363583, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 1
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 1
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 1
  m_InteractionAttachController: {fileID: 1795399876645144408}
  m_EnableNearCasting: 1
  m_NearInteractionCaster: {fileID: 7648912439904453997}
  m_NearCasterSortingStrategy: 1
  m_SortNearTargetsAfterTargetFilter: 0
  m_EnableFarCasting: 1
  m_FarInteractionCaster: {fileID: 1731497579831748185}
  m_FarAttachMode: 1
  m_EnableUIInteraction: 1
  m_BlockUIOnInteractableSelection: 1
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_UIPressInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: ded1ccb2-ff18-46c7-ade9-b80985fe2825
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: d762660e-30e0-4a4d-8e2a-e6b553e03f11
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: -6395602842196007441, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: 71106601250685021, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: ef5cc4a5-b968-432c-9ae7-45e494178db0
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 2464016903823916871, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
--- !u!1 &1072080526943758045
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 545667201402663227}
  - component: {fileID: 5187423859371698993}
  - component: {fileID: 8075031838612634213}
  - component: {fileID: 6393680207812758106}
  - component: {fileID: 2202696942498995150}
  - component: {fileID: 5765025255924269130}
  - component: {fileID: 296868951552395432}
  - component: {fileID: 4574743199282298143}
  m_Layer: 0
  m_Name: Right Controller
  m_TagString: RightHand
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1089412022518228957
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2782230393457384253}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00042568197, y: 0.013497432, z: 0.000091881804, w: 0.9999088}
  m_LocalPosition: {x: 0.000000024472824, y: 0.021747325, z: -0.00000014699962}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2788367612727919741}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1101418946447317244
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8233017956366635696}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.085706234, y: 0.4955097, z: 0.8480016}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4026006277366733104}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1107543382018816462
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433312847804040521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b734f2bd29eeddd4d85afb0c266228c3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HapticOutput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Haptic
      m_Type: 2
      m_ExpectedControlType: 
      m_Id: a67d36a7-d7d4-428e-877d-0cad8d4a162f
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8785819595477538065, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
  m_AmplitudeMultiplier: 1
--- !u!1 &1117558224355886537
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4751113485835488039}
  - component: {fileID: 1286101557563369067}
  - component: {fileID: 4526834763291727596}
  m_Layer: 2
  m_Name: Turn
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1165540589783183136
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433312847804040521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61498d7f708724f568add451bdb149c9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactor: {fileID: 0}
  chipSelectionManager: {fileID: 8923731902986500387}
  targetGameObjectTag: Chip
--- !u!1 &1169109927468303437
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7265973089823045129}
  - component: {fileID: 7812155368381269535}
  - component: {fileID: 260707104338999143}
  - component: {fileID: 2339269481727074501}
  - component: {fileID: 1798731436442317273}
  - component: {fileID: 8896671373857680896}
  m_Layer: 0
  m_Name: Near-Far Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &1179894378618492641
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2510119848456018298}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 760ff70c1c91bdd45907d0ff0cdcaf7f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 7739599882840324738}
  m_TransformationPriority: 0
  m_EnableFreeXMovement: 1
  m_EnableFreeYMovement: 0
  m_EnableFreeZMovement: 1
  m_UseGravity: 1
  m_GravityApplicationMode: 0
  m_LeftGrabMoveProvider: {fileID: 291898865573910355}
  m_RightGrabMoveProvider: {fileID: 7437920921242551290}
  m_OverrideSharedSettingsOnInit: 1
  m_MoveFactor: 1
  m_RequireTwoHandsForTranslation: 0
  m_EnableRotation: 1
  m_EnableScaling: 0
  m_MinimumScale: 0.2
  m_MaximumScale: 5
--- !u!4 &1228367496959184203
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3788156388887934598}
  serializedVersion: 2
  m_LocalRotation: {x: -0.004415343, y: -0.08397641, z: 0.027910044, w: 0.996067}
  m_LocalPosition: {x: 0.0000000019207507, y: 0.1496083, z: 0.000000087759275}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 715742585284539564}
  m_Father: {fileID: 4391677642099535108}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1236817472662357903
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6110409109699714452}
  m_Layer: 0
  m_Name: RightHandPinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1249094729052492475
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6291295526486202380}
  - component: {fileID: 1392065700199845922}
  m_Layer: 0
  m_Name: Right Controller Teleport Stabilized Origin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!1 &1270683876034729651
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4248990946545048892}
  - component: {fileID: 175434157854035718}
  m_Layer: 0
  m_Name: Gaze Stabilized
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &1286101557563369067
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1117558224355886537}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9f365cf844c03449bc8973eead2c3c1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 7739599882840324738}
  m_TransformationPriority: 0
  m_TurnAmount: 45
  m_DebounceTime: 0.5
  m_EnableTurnLeftRight: 1
  m_EnableTurnAround: 1
  m_DelayTime: 0
  m_LeftHandTurnInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Left Hand Snap Turn
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 536e141d-ee23-4272-b0fd-3984d1655f02
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -7374733323251553461, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RightHandTurnInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Right Hand Snap Turn
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: b17ca378-4740-48c7-abe1-7f35bce317e9
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8525429354371678379, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
--- !u!4 &1286356308969779788
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2475568010325282552}
  serializedVersion: 2
  m_LocalRotation: {x: 0.013443346, y: -0.015071049, z: -0.00070792745, w: 0.9997958}
  m_LocalPosition: {x: 0.00000007205937, y: 0.037637394, z: -0.000000023956796}
  m_LocalScale: {x: 0.9999999, y: 0.9999998, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3516606857722637648}
  m_Father: {fileID: 5777818085006005823}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1294047678815427870
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8020169616977755655}
  serializedVersion: 2
  m_LocalRotation: {x: 0.2600679, y: -0.0013405392, z: 0.003938123, w: 0.9655814}
  m_LocalPosition: {x: -0.00014529144, y: 0.15952566, z: -0.0000030789097}
  m_LocalScale: {x: 0.9999999, y: 0.9999997, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9000211819660584494}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!222 &1359499219724598566
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2607784414646659857}
  m_CullTransparentMesh: 1
--- !u!1 &1362816434783236798
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1959705464501603054}
  - component: {fileID: 378383395634389564}
  m_Layer: 0
  m_Name: Left_Leg_IK_target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1392065700199845922
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1249094729052492475}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 64d299502104b064388841ec2adf6def, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Target: {fileID: 5994736955204494484}
  m_AimTargetObject: {fileID: 4509087775236993182}
  m_UseLocalSpace: 0
  m_AngleStabilization: 20
  m_PositionStabilization: 0.25
--- !u!1 &1394576125987870698
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 535261126972462885}
  m_Layer: 0
  m_Name: Gaze Stabilized Attach
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1436625747208450544
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7004919098972313426}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a07099be03305b41a07937279c2c105, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _avatarReceiver: {fileID: 6589592714198776237}
  _onAvatarReceived:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1527898246755899815}
        m_TargetAssemblyTypeName: Avaturn.Core.Runtime.Scripts.Avatar.DownloadAvatar,
          Avaturn.Core.Runtime
        m_MethodName: Download
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 8035523432992933076}
        m_TargetAssemblyTypeName: Avaturn.Core.Runtime.Scripts.Avatar.Data.DrawAvatarInfo,
          Avaturn.Core.Runtime
        m_MethodName: Draw
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _downloadAvatar: {fileID: 1527898246755899815}
  _onAvatarDownloaded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 365169162618499662}
        m_TargetAssemblyTypeName: Avaturn.Core.Runtime.Scripts.Avatar.PrepareAvatar,
          Avaturn.Core.Runtime
        m_MethodName: PrepareModel
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _subdomain: demo
  _linkFromAPI: 
  _webViewGameObject: {fileID: 4635425379091036855}
  _webViewFrame: {fileID: 3032641275601302522}
  _webViewPrefab: {fileID: 0}
  webViewObject: {fileID: 0}
  trargetURL: 
  targetDomain: 
  isEditorTest: 0
--- !u!114 &1436958524581276553
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433312847804040521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9ac216f0eb04754b1d938aac6380b31, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RayInteractor: {fileID: 0}
  m_NearFarInteractor: {fileID: 1062557283441490215}
  m_TeleportInteractor: {fileID: 1062335248880780195}
  m_TeleportMode: {fileID: 1263111715868034790, guid: c348712bda248c246b8c49b3db54643f,
    type: 3}
  m_TeleportModeCancel: {fileID: 737890489006591557, guid: c348712bda248c246b8c49b3db54643f,
    type: 3}
  m_Turn: {fileID: 1010738217276881514, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SnapTurn: {fileID: -7374733323251553461, guid: c348712bda248c246b8c49b3db54643f,
    type: 3}
  m_Move: {fileID: 6972639530819350904, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIScroll: {fileID: 2464016903823916871, guid: c348712bda248c246b8c49b3db54643f,
    type: 3}
  m_SmoothMotionEnabled: 1
  m_SmoothTurnEnabled: 0
  m_NearFarEnableTeleportDuringNearInteraction: 1
  m_UIScrollingEnabled: 1
  m_RayInteractorChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!4 &1437907489910130082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8443053389285184463}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.02, z: -0.035}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 545667201402663227}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1441413611533425565
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5703946418305378629}
  - component: {fileID: 1545155000075760272}
  - component: {fileID: 5495894395949649344}
  m_Layer: 5
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1454032667787034614
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4691006411662421713}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1344c3c82d62a2a41a3576d8abb8e3ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Texture: {fileID: 0}
  m_UVRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
--- !u!4 &1460506307876685992
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7580238669463855221}
  serializedVersion: 2
  m_LocalRotation: {x: 0.014314166, y: -0.046918914, z: -0.025612382, w: 0.9984677}
  m_LocalPosition: {x: 0.019884976, y: 0.08415067, z: 0.001199128}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2788367612727919741}
  m_Father: {fileID: 279653332813315513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1508358508567715262
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5242014224930435635}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3136410957928493084}
  - {fileID: 5477248990418324633}
  - {fileID: 7780604994006172390}
  - {fileID: 6427239352650257207}
  m_Father: {fileID: 2072851329618579022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1514165820928196898
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 496353250390962969}
  m_Layer: 0
  m_Name: Camera Offset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1521370347279902152
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4919935681502709809}
  - component: {fileID: 8162697028205064111}
  m_Layer: 0
  m_Name: renderCanvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1527898246755899815
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5160961464378193121}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2c70b069cdd5ec42bcfd2108d788b8b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1534436147016792683
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3447113454583995759}
  m_Layer: 0
  m_Name: RightHandMiddle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!222 &1545155000075760272
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1441413611533425565}
  m_CullTransparentMesh: 1
--- !u!1 &1546219791638165760
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1979724344501094098}
  - component: {fileID: 3078038043639807932}
  - component: {fileID: 4764869234962504907}
  m_Layer: 0
  m_Name: Background
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1589479454740688656
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2005964267215810734}
  - component: {fileID: 7797587527884744427}
  m_Layer: 0
  m_Name: Left Controller Teleport Stabilized Origin
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!1 &1600680041059445812
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7730492227343333247}
  - component: {fileID: 6581999353183459650}
  - component: {fileID: 3607444220109197260}
  m_Layer: 0
  m_Name: playerName
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1642833139858708784
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4691006411662421713}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70d2e4700f40744fe8b999f209d0253b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &1650976106500663011
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2644425797370303675}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6291295526486202380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1676166793883116007
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9205554656255805001}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0924bcaa9eb50df458a783ae0e2b59f5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 1
  m_Handedness: 1
  m_AttachTransform: {fileID: 4452282283316580681}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_PokeDepth: 0.1
  m_PokeWidth: 0.0075
  m_PokeSelectWidth: 0.015
  m_PokeHoverRadius: 0.015
  m_PokeInteractionOffset: 0.005
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_PhysicsTriggerInteraction: 1
  m_RequirePokeFilter: 1
  m_EnableUIInteraction: 1
  m_DebugVisualizationsEnabled: 0
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
--- !u!23 &1678804837777675695
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044456232784710588}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1689585148759238803
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8760523660980945685}
  - component: {fileID: 5683243799015076350}
  - component: {fileID: 6206877780613868119}
  - component: {fileID: 8268868808138030560}
  m_Layer: 5
  m_Name: ShowButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1696983009162425143
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6259267943173207598}
  m_Layer: 0
  m_Name: LeftHandPinky3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1727065827532048605
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1867588534553549969}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2072851329618579022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &1727364723895473379
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2510119848456018298}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8473767348284954952}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1729026098378324245
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 017c5e3933235514c9520e1dace2a4b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ActionAssets:
  - {fileID: -944628639613478452, guid: c348712bda248c246b8c49b3db54643f, type: 3}
--- !u!114 &1731497579831748185
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6966779063830867726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef20135915079454985abea5a2ec8967, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_CastOrigin: {fileID: 6327557944332572447}
  m_EnableStabilization: 1
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_AimTargetObject: {fileID: 1062557283441490215}
  m_RaycastMask:
    serializedVersion: 2
    m_Bits: 2147483681
  m_RaycastTriggerInteraction: 1
  m_RaycastSnapVolumeInteraction: 1
  m_TargetNumCurveSegments: 1
  m_HitDetectionType: 2
  m_CastDistance: 10
  m_SphereCastRadius: 0.1
  m_ConeCastAngle: 6
  m_LiveConeCastDebugVisuals: 0
--- !u!4 &1765414289509432991
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3381608978322424781}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000059604645, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.008775876, y: -0.002558912, z: -0.0074315914}
  m_LocalScale: {x: 1.342947, y: 1.342947, z: 1.342947}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2177334080802346132}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1766749749359418194
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6854304056576790987}
  - component: {fileID: 150833435104077431}
  m_Layer: 0
  m_Name: Slider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!120 &1774017295705756523
LineRenderer:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545711285861446478}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10306, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions: []
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 1
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0.012002945
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 8
    numCapVertices: 8
    alignment: 0
    textureMode: 0
    textureScale: {x: 1, y: 1}
    shadowBias: 0.5
    generateLightingData: 0
  m_MaskInteraction: 0
  m_UseWorldSpace: 1
  m_Loop: 0
  m_ApplyActiveColorSpace: 0
--- !u!1 &1786676441828673123
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3758277795233954449}
  - component: {fileID: 3037633381448913751}
  m_Layer: 0
  m_Name: SelectionImage
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1788040821291060895
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5356247828354287878}
  - component: {fileID: 2234876244449394634}
  m_Layer: 0
  m_Name: Pinch_Pointer_LOD0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1795399876645144408
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6966779063830867726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 792f6c7eaa1a4b82abf8351559ac97eb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransformToFollow: {fileID: 6327557944332572447}
  m_MotionStabilizationMode: 1
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_SmoothOffset: 0
  m_SmoothingSpeed: 10
  m_UseDistanceBasedVelocityScaling: 1
  m_UseMomentum: 1
  m_MomentumDecayScale: 1.25
  m_ZVelocityRampThreshold: 0.3
  m_PullVelocityBias: 1
  m_PushVelocityBias: 1.25
  m_MinAdditionalVelocityScalar: 0.05
  m_MaxAdditionalVelocityScalar: 1.5
  m_EnableDebugLines: 0
--- !u!4 &1796092491595218133
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4268915084993462964}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.012954317, z: -0.02}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 318914851436607795}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1798731436442317273
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169109927468303437}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ef20135915079454985abea5a2ec8967, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_CastOrigin: {fileID: 7265973089823045129}
  m_EnableStabilization: 1
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_AimTargetObject: {fileID: 7812155368381269535}
  m_RaycastMask:
    serializedVersion: 2
    m_Bits: 2147483681
  m_RaycastTriggerInteraction: 1
  m_RaycastSnapVolumeInteraction: 1
  m_TargetNumCurveSegments: 1
  m_HitDetectionType: 2
  m_CastDistance: 10
  m_SphereCastRadius: 0.1
  m_ConeCastAngle: 6
  m_LiveConeCastDebugVisuals: 0
--- !u!114 &1838482847470871544
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4390012197431013410}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
--- !u!114 &1839183715752652042
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1adda2477490416f93b1ac5535f5e586, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1867588534553549969
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1727065827532048605}
  - component: {fileID: 8503045284414179774}
  - component: {fileID: 5296241919768237731}
  m_Layer: 0
  m_Name: Controller_Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &1868339462333439342
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4514296614285512320}
  - component: {fileID: 6567829091087403004}
  m_Layer: 0
  m_Name: Head_IK
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1899011120009701176
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3731247915614967799}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7951c64acb0fa62458bf30a60089fe2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 0
  m_CheckFor2DOcclusion: 0
  m_CheckFor3DOcclusion: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RaycastTriggerInteraction: 1
--- !u!210 &1959180444716697021
SortingGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 484607128982752228}
  m_Enabled: 1
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 30005
  m_SortAtRoot: 0
--- !u!4 &1959705464501603054
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1362816434783236798}
  serializedVersion: 2
  m_LocalRotation: {x: -0.011284724, y: -0.5117987, z: -0.85900503, w: -0.0067234845}
  m_LocalPosition: {x: -0.085706234, y: 0.09145409, z: -0.011317313}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4026006277366733104}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1963304754779788005
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2607784414646659857}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.45490196}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!224 &1979724344501094098
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1546219791638165760}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6854304056576790987}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &2005964267215810734
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1589479454740688656}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2011203541378567110}
  m_Father: {fileID: 496353250390962969}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2011203541378567110
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5325505344789907886}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2005964267215810734}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2027895492383954671
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6606507566438310603}
  m_Layer: 0
  m_Name: LeftHandMiddle3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2041486599090187651
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7577900487402064988}
  m_Layer: 0
  m_Name: LeftHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2061924075854312818
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3586357643331768374}
  - component: {fileID: 8730272792476605426}
  m_Layer: 0
  m_Name: Right Controller Visual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2072851329618579022
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2448911749658170476}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6790821682542554873}
  - {fileID: 4783094969730892873}
  - {fileID: 1727065827532048605}
  - {fileID: 3461694122426595715}
  - {fileID: 6829439297224888606}
  - {fileID: 1508358508567715262}
  m_Father: {fileID: 3539810304606826006}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2073727678213263734
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7614375382012231164}
  m_Mesh: {fileID: -8653722315008560443, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!1 &2083018880704276259
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 119037776445706993}
  - component: {fileID: 3730042378119755578}
  - component: {fileID: 57552291714031914}
  m_Layer: 0
  m_Name: Text (Legacy)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!1 &2088670065357337757
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6201949576961765177}
  - component: {fileID: 6973807910609024344}
  - component: {fileID: 2703716912472870659}
  m_Layer: 0
  m_Name: Poke Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2089684147691075087
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 966853248386523949}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 879d31ebe92b040cbb3ef97e98278140, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  InitialUrl: https://demo.avaturn.dev?sdk=true
  DragMode: 0
  ClickingEnabled: 1
  HoveringEnabled: 1
  ScrollingEnabled: 1
  DragThreshold: 20
  RemoteDebuggingEnabled: 0
  LogConsoleMessages: 0
  _cachedVideoLayer: {fileID: 0}
  _cachedView: {fileID: 0}
  _pointerInputDetectorMonoBehaviour: {fileID: 0}
  _webViewGameObject: {fileID: 0}
  Native2DModeEnabled: 0
  NativeOnScreenKeyboardEnabled: 1
  InitialResolution: 1
  ScrollingSensitivity: 15
--- !u!1 &2150014697992355620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4452282283316580681}
  m_Layer: 0
  m_Name: Poke Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2153578759948281100
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2620207173407796051}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6a26c941eb8a46f7b6d00416227ab8c0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_XROrigin: {fileID: 8171373516390849214}
  m_BodyPositionEvaluatorObject: {fileID: 0}
  m_ConstrainedBodyManipulatorObject: {fileID: 0}
  m_UseCharacterControllerIfExists: 1
--- !u!114 &2154573498142499198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4635425379091036855}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 598e18fb001004a81960f552978ecf4e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  urlOnStart: 
  showOnStart: 0
  fullScreen: 1
  useToolbar: 0
  toolbarPosition: 0
  useEmbeddedToolbar: 1
  embeddedToolbarPosition: 0
  frame:
    serializedVersion: 2
    x: 0
    y: 0
    width: 0
    height: 0
  referenceRectTransform: {fileID: 0}
--- !u!4 &2177334080802346132
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2814601935524765685}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6165861432464125509}
  - {fileID: 6141893153934801516}
  - {fileID: 1765414289509432991}
  - {fileID: 6142710786534630359}
  m_Father: {fileID: 318914851436607795}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2184974203031281454
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433312847804040521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4253f32900bcc4d499d675566142ded0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 4338852539094006615}
  m_InteractionLayers:
    m_Bits: 4294967295
  m_Handedness: 0
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 29526732-baa1-4cef-8a29-1d8260a7a4fd
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: e9e925bd-58f9-4df4-88cf-a896d72ccaa7
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 29728087-8a44-4838-9d4a-b294777ecfa9
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 47fd239c-3012-4ef8-8745-d48ebb60efe5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 1
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 1
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 1
  m_ImproveAccuracyWithSphereCollider: 0
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_PhysicsTriggerInteraction: 1
--- !u!114 &2202696942498995150
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1072080526943758045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c2fadf230d1919748a9aa21d40f74619, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackingType: 0
  m_UpdateType: 0
  m_IgnoreTrackingState: 0
  m_PositionInput:
    m_UseReference: 1
    m_Action:
      m_Name: Position
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: c21ad7d3-c46f-48ad-99da-097df39312cc
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -3326005586356538449, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_RotationInput:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 69f091ba-e646-4189-9296-d68870a4d922
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 5101698808175986029, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_TrackingStateInput:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: 05873c58-6d6f-4aea-8e06-78c4544c58dc
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -1277054153949319361, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_PositionAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: e0c62bbd-29f9-48af-967b-21babef9a6a4
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  m_RotationAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 303ef588-e0d6-494e-9ceb-2d2edc0eb8e5
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
--- !u!224 &2207550626579633132
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 966853248386523949}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 194626214125372492}
  - {fileID: 2995845308996088401}
  m_Father: {fileID: 7062042876505683019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: -54.09466}
  m_SizeDelta: {x: 0, y: -108.1893}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!137 &2234876244449394634
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1788040821291060895}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: f5ccd52dc494e054fbe7d7161dcabe25, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -5120762275383614272, guid: e053b8fbc416ba349b4a58a26410bba2, type: 3}
  m_Bones: []
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 0}
  m_AABB:
    m_Center: {x: 0, y: 0.009045093, z: 0}
    m_Extent: {x: 0.0077457884, y: 0.016694028, z: 0.0077457884}
  m_DirtyAABB: 0
--- !u!4 &2238439274622436440
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7605991476053792939}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.444777, y: 1.5764731, z: -0.54538316}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5347058672833980954}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &2242803550899765549
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2833875080643800683}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2260743472254961591
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7322571774275400544}
  m_Layer: 0
  m_Name: LeftHandRing2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2271816024791054615
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8761334942438416270}
  - component: {fileID: 4278881197966947620}
  m_Layer: 0
  m_Name: Pinch_Pointer_LOD0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!95 &2307306175086723115
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5672919205124789578}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 1720c98616a0f194dae551fe7645b49b, type: 2}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &2310486626309057212
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7843615889005886009}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!4 &2333636119849558983
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2354062419317501375}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000000017511017, y: -0.0027354355, z: -0.99999624, w: -0.000000026945838}
  m_LocalPosition: {x: -0.08570601, y: -0.030165832, z: 0.0005758305}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5061350368787562714}
  m_Father: {fileID: 4975108577388796292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!222 &2336984865471353042
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6238845496079627304}
  m_CullTransparentMesh: 1
--- !u!114 &2339269481727074501
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169109927468303437}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 48139a683d3b4ac3a37cd5d24f71acf1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_CastOrigin: {fileID: 7265973089823045129}
  m_EnableStabilization: 0
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_AimTargetObject: {fileID: 0}
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_PhysicsTriggerInteraction: 1
  m_CastRadius: 0.1
--- !u!1 &2341254861155593623
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7104329100137566756}
  - component: {fileID: 2668930533464041367}
  m_Layer: 0
  m_Name: Right_Leg_IK_target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2341674191280902196
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4105869907717761748}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3304550752943941888}
  m_Father: {fileID: 6854304056576790987}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2351755904764105178
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3652913999594940763}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd0b9921bce4eeb49bd05815b1135ac2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractorSourceObject: {fileID: 1062335248880780195}
  m_HapticImpulsePlayer: {fileID: 1107543382018816462}
  m_PlaySelectEntered: 1
  m_SelectEnteredData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectExited: 0
  m_SelectExitedData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectCanceled: 0
  m_SelectCanceledData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverEntered: 1
  m_HoverEnteredData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverExited: 0
  m_HoverExitedData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverCanceled: 0
  m_HoverCanceledData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_AllowHoverHapticsWhileSelecting: 1
--- !u!114 &2354001825908910687
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 134444605805926517}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86d13fca2ec430d870c0f7765ad0dde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 361403495987376044}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: idle
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hovered
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hoveredPriority
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: selected
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: activated
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: focused
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      m_ColorBlendMode: 0
      m_BlendAmount: 1
    m_Variable: {fileID: 11400000, guid: fc690d1505c48cb4696838b71abd2ca0, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 7852162772088412140}
  m_ColorPropertyName: _BaseColor
--- !u!1 &2354062419317501375
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2333636119849558983}
  m_Layer: 0
  m_Name: LeftUpLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2401820348149325874
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8142670312825056300}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4a5f76f9ea8c80547973ab01877f9567, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ThumbstickTransform: {fileID: 7780604994006172390}
  m_StickRotationRange: {x: 30, y: -30}
  m_StickInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Thumbstick
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 2f424cdc-fe47-4989-a9c2-6fa97f609e70
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -60998027439631388, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_TriggerTransform: {fileID: 6829439297224888606}
  m_TriggerXAxisRotationRange: {x: 0, y: -15}
  m_TriggerInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Trigger
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: adbe6783-3cbb-48f7-8ad5-0b09a32b9943
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -4289430672226363583, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
  m_GripTransform: {fileID: 6790821682542554873}
  m_GripRightRange: {x: -0.0125, y: -0.011}
  m_GripInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Grip
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 7ddc77c6-8f81-4889-aaca-964286dbe1ea
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 6558622148059887818, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
--- !u!4 &2420031529279389316
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 134444605805926517}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5164021330966194647}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2448003236372656183
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 82bc72d2ecc8add47b2fe00d40318500, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LeftHand: {fileID: 0}
  m_RightHand: {fileID: 0}
  m_LeftController: {fileID: 433312847804040521}
  m_RightController: {fileID: 1072080526943758045}
  m_TrackedHandModeStarted:
    m_PersistentCalls:
      m_Calls: []
  m_TrackedHandModeEnded:
    m_PersistentCalls:
      m_Calls: []
  m_MotionControllerModeStarted:
    m_PersistentCalls:
      m_Calls: []
  m_MotionControllerModeEnded:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &2448911749658170476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2072851329618579022}
  m_Layer: 0
  m_Name: UniversalController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2475568010325282552
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1286356308969779788}
  m_Layer: 0
  m_Name: RightHandIndex2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &2481944593839669219
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4268915084993462964}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2510119848456018298
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1727364723895473379}
  - component: {fileID: 291898865573910355}
  - component: {fileID: 7437920921242551290}
  - component: {fileID: 1179894378618492641}
  m_Layer: 2
  m_Name: Grab Move
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!1 &2511277965148420841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3993044746207940765}
  - component: {fileID: 9220416919784355882}
  m_Layer: 0
  m_Name: Right_Leg_IK
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!210 &2540319103546819079
SortingGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8443053389285184463}
  m_Enabled: 1
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 30005
  m_SortAtRoot: 0
--- !u!1 &2557475164967223925
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 577758435368920075}
  - component: {fileID: 6386483533056644797}
  - component: {fileID: 8436963578917438688}
  m_Layer: 5
  m_Name: Instruction
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2607784414646659857
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2609061317010167862}
  - component: {fileID: 1359499219724598566}
  - component: {fileID: 1963304754779788005}
  - component: {fileID: 3850661809983654175}
  m_Layer: 5
  m_Name: PlatformInfo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2609061317010167862
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2607784414646659857}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 577758435368920075}
  m_Father: {fileID: 7062042876505683019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 86.5}
  m_SizeDelta: {x: 0, y: 62.1}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &2620207173407796051
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8473767348284954952}
  - component: {fileID: 7739599882840324738}
  - component: {fileID: 2153578759948281100}
  m_Layer: 2
  m_Name: Locomotion
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2620991336296125861
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6031087458372589770}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4975108577388796292}
  - {fileID: 5255911528893758561}
  - {fileID: 8274526277103049002}
  - {fileID: 3767527757336721719}
  - {fileID: 3554821131344161157}
  m_Father: {fileID: 8506148699809443934}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2644425797370303675
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1650976106500663011}
  m_Layer: 0
  m_Name: Right Controller Stabilized Attach
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2645001781349080075
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3754479819261742745}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b3ae643b7060a541907c4f1825e5dbc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2654499417665259012
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5849612435864072034}
  - component: {fileID: 8514636028103646756}
  m_Layer: 0
  m_Name: ChipStackHolder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2668930533464041367
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2341254861155593623}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 95a8098103f299b44be14d0de50e7fe3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  isMovingForward: 0
  terrainLayer:
    serializedVersion: 2
    m_Bits: 1
  body: {fileID: 4285635575700484274}
  otherFoot: {fileID: 378383395634389564}
  speed: 4
  stepDistance: 0.2
  stepLength: 0.2
  sideStepLength: 0.1
  stepHeight: 0.3
  footOffset: {x: 0, y: 0, z: 0}
  footRotOffset: {x: -120, y: 180, z: 0}
  footYPosOffset: 0.1
  rayStartYOffset: 0
  rayLength: 1.5
--- !u!4 &2673107099597323512
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5160961464378193121}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7564145236229543277}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!120 &2694127015239756176
LineRenderer:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 484607128982752228}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10306, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions: []
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 1
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0.012002945
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 8
    numCapVertices: 8
    alignment: 0
    textureMode: 0
    textureScale: {x: 1, y: 1}
    shadowBias: 0.5
    generateLightingData: 0
  m_MaskInteraction: 0
  m_UseWorldSpace: 1
  m_Loop: 0
  m_ApplyActiveColorSpace: 0
--- !u!33 &2703183126586657928
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318703313496157382}
  m_Mesh: {fileID: -8429650256770907399, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!114 &2703716912472870659
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2088670065357337757}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd0b9921bce4eeb49bd05815b1135ac2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractorSourceObject: {fileID: 6973807910609024344}
  m_HapticImpulsePlayer: {fileID: 6393680207812758106}
  m_PlaySelectEntered: 1
  m_SelectEnteredData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectExited: 0
  m_SelectExitedData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectCanceled: 0
  m_SelectCanceledData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverEntered: 1
  m_HoverEnteredData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverExited: 0
  m_HoverExitedData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverCanceled: 0
  m_HoverCanceledData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_AllowHoverHapticsWhileSelecting: 0
--- !u!4 &2741029711676812753
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5807584919204290509}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4613608991934880773}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2745325864379839978
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 71643286533970796}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!4 &2749870845763744358
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 712250697776636523}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00069871225, y: 0.010598071, z: 0.0004980663, w: 0.9999435}
  m_LocalPosition: {x: 0.000000020924945, y: 0.02671359, z: 0.00000025171283}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6259267943173207598}
  m_Father: {fileID: 6596787623609719101}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2761774296399923716
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 715742585284539564}
  m_Layer: 0
  m_Name: LeftForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2782230393457384253
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1089412022518228957}
  m_Layer: 0
  m_Name: RightHandRing3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2788367612727919741
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3876710783076775415}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00059305126, y: 0.008879056, z: -0.00084160746, w: 0.99996006}
  m_LocalPosition: {x: 0.000000021214028, y: 0.034392804, z: 0.00000024951603}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1089412022518228957}
  m_Father: {fileID: 1460506307876685992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &2808095484452820845
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433086827229989035}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0006256521, y: 0.0007064468, z: -0.0003100922, w: 0.9999995}
  m_LocalPosition: {x: -0.000000034863536, y: 0.021179488, z: 0.00000006772609}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 634068311700983852}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2814601935524765685
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2177334080802346132}
  m_Layer: 0
  m_Name: XRController_Thumbstick_Buttons
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2829692256499286248
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5741279186860494031}
  - component: {fileID: 51034587480286600}
  - component: {fileID: 833644704101840033}
  m_Layer: 0
  m_Name: Controller_Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &2833875080643800683
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3461694122426595715}
  - component: {fileID: 7373554196297813687}
  - component: {fileID: 2242803550899765549}
  m_Layer: 0
  m_Name: TouchPad
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2947483358204636977
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3886752523380519911}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02806403, y: 0.004269741, z: 0.01624564, w: 0.999465}
  m_LocalPosition: {x: -0.000000014341268, y: 0.025791943, z: 0.000000018458918}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4380871466541375476}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!224 &2995845308996088401
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4691006411662421713}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2207550626579633132}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!224 &3032641275601302522
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4744729318664804077}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7062042876505683019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 45}
  m_SizeDelta: {x: 0, y: -90}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3037633381448913751
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1786676441828673123}
  m_CullTransparentMesh: 1
--- !u!114 &3067972690501691380
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4916089409233462726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!222 &3078038043639807932
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1546219791638165760}
  m_CullTransparentMesh: 1
--- !u!224 &3086063312870280375
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 70538000784449429}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6137808655463954828}
  m_Father: {fileID: 6854304056576790987}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0.25}
  m_AnchorMax: {x: 1, y: 0.75}
  m_AnchoredPosition: {x: -5, y: 0}
  m_SizeDelta: {x: -20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &3136410957928493084
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3203986923887316783}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.007800013, y: 0.0013757758, z: 0.0055}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1508358508567715262}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3155363803605568173
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3229396878313706096}
  serializedVersion: 2
  m_LocalRotation: {x: 0.15590718, y: 0.09437984, z: -0.3916488, w: 0.90188503}
  m_LocalPosition: {x: 0.02065989, y: 0.03584229, z: 0.014225291}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6643006642981370824}
  m_Father: {fileID: 7577900487402064988}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3196501066792115639
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3885301077535107034}
  m_Mesh: {fileID: -2014588322676101042, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!1 &3203986923887316783
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3136410957928493084}
  - component: {fileID: 8375457624469957523}
  - component: {fileID: 8253591638352346841}
  m_Layer: 0
  m_Name: Button_A
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &3229396878313706096
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3155363803605568173}
  m_Layer: 0
  m_Name: LeftHandThumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &3232823210907559783
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8636585185768493816}
  - component: {fileID: 8502848787180089549}
  - component: {fileID: 6424885846941238709}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3304550752943941888
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3988972325260091870}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2341674191280902196}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 20, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &3354135046105739863
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5610636252869755943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fff0960ef4ea6e04eac66b4a7fd2189d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RigLayers:
  - m_Rig: {fileID: 8904514290230835218}
    m_Active: 1
  m_Effectors: []
--- !u!1 &3374826707889493468
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5668317968300891774}
  - component: {fileID: 640092585440998972}
  m_Layer: 0
  m_Name: ChipStackHolder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &3381608978322424781
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1765414289509432991}
  - component: {fileID: 6974596716163073188}
  - component: {fileID: 5253460690420914100}
  m_Layer: 0
  m_Name: ThumbStick
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3421309572655730664
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6930141933070963737}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07495686, y: 0.00000017530881, z: 0.000000008709882, w: 0.9971868}
  m_LocalPosition: {x: -6.896794e-15, y: 0.16200231, z: -0.000000039171336}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 559369952382032964}
  - {fileID: 4391677642099535108}
  - {fileID: 8978555451093852455}
  m_Father: {fileID: 8194709199298547771}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!81 &3424102171925712838
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4390012197431013410}
  m_Enabled: 1
--- !u!4 &3446222053752739363
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6090362364418762880}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5347058672833980954}
  - {fileID: 7992904332792918524}
  - {fileID: 4514296614285512320}
  - {fileID: 3993044746207940765}
  - {fileID: 4026006277366733104}
  m_Father: {fileID: 4285635575700484274}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3447113454583995759
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1534436147016792683}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0062167314, y: 0.0027872554, z: -0.0037099149, w: 0.9999699}
  m_LocalPosition: {x: 0.000000037438845, y: 0.03999125, z: 0.0000000744791}
  m_LocalScale: {x: 0.9999999, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7161254564539608682}
  m_Father: {fileID: 5911338096449038408}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3461694122426595715
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2833875080643800683}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.0020741627, z: -0.0052528577}
  m_LocalScale: {x: 0.982392, y: 1.55, z: 0.982392}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2072851329618579022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3506104228170955532
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5777818085006005823}
  m_Layer: 0
  m_Name: RightHandIndex1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3506797319206247025
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3853495253572125805}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7851588616201146884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3516606857722637648
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7003079085475866073}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0006256481, y: -0.0007064458, z: 0.0003100935, w: 0.9999995}
  m_LocalPosition: {x: 0.000000027783143, y: 0.021179512, z: 0.00000011470721}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1286356308969779788}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3533296154301763878
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5303906344900895163}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8473767348284954952}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3539810304606826006
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8142670312825056300}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 1, z: 0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: -0.05}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2072851329618579022}
  m_Father: {fileID: 6706157922200873912}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3551008720678513838
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6596787623609719101}
  m_Layer: 0
  m_Name: LeftHandPinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3554821131344161157
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4657654078849255502}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2620991336296125861}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3564190040523313381
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7428536110082899187}
  m_Layer: 0
  m_Name: Left_Hand_IK_hint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3586357643331768374
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2061924075854312818}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 1, z: 0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: -0.05}
  m_LocalScale: {x: -1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 318914851436607795}
  m_Father: {fileID: 545667201402663227}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3607444220109197260
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600680041059445812}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 256
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 0
    m_MaxSize: 256
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Kapil Khatik
--- !u!4 &3640138343546388506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 592124764034376607}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8473767348284954952}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3652913999594940763
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5937777216826553390}
  - component: {fileID: 1062335248880780195}
  - component: {fileID: 6361084006152553736}
  - component: {fileID: 8385989862410996468}
  - component: {fileID: 4695113682247617013}
  - component: {fileID: 2351755904764105178}
  m_Layer: 0
  m_Name: Teleport Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &3674717633635777532
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545711285861446478}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 80e353695beb436ab39a90d9ecefaee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LineRenderer: {fileID: 1774017295705756523}
  m_CurveVisualObject: {fileID: 7812155368381269535}
  m_OverrideLineOrigin: 1
  m_LineOriginTransform: {fileID: 7265973089823045129}
  m_VisualPointCount: 20
  m_MaxVisualCurveDistance: 10
  m_RestingVisualLineLength: 0.25
  m_LineDynamicsMode: 1
  m_RetractDelay: 1
  m_RetractDuration: 1
  m_ExtendLineToEmptyHit: 0
  m_ExtensionRate: 10
  m_EndPointExpansionRate: 10
  m_ComputeMidPointWithComplexCurves: 0
  m_SnapToSelectedAttachIfAvailable: 1
  m_SnapToSnapVolumeIfAvailable: 1
  m_CurveStartOffset: 0.015
  m_CurveEndOffset: 0.005
  m_CustomizeLinePropertiesForState: 1
  m_LinePropertyAnimationSpeed: 8
  m_NoValidHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.25
    m_AdjustWidth: 1
    m_StarWidth: 0.003
    m_EndWidth: 0.003
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 1, g: 1, b: 1, a: 0.5019608}
      key2: {r: 0, g: 0, b: 0, a: 0.2509804}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 193
      atime1: 8192
      atime2: 32768
      atime3: 55705
      atime4: 65342
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 2
      m_NumAlphaKeys: 4
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_UIHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.5
    m_AdjustWidth: 1
    m_StarWidth: 0.004
    m_EndWidth: 0.004
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 1, g: 0.78431374, b: 0.5686275, a: 0.49019608}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 16384
      ctime2: 65535
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8192
      atime2: 32768
      atime3: 65535
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 3
      m_NumAlphaKeys: 4
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_UIPressHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.5
    m_AdjustWidth: 1
    m_StarWidth: 0.003
    m_EndWidth: 0.003
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 0.5686275, g: 0.78431374, b: 1, a: 0.627451}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 1, g: 1, b: 1, a: 0.78431374}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 32768
      ctime2: 65535
      ctime3: 65535
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8192
      atime2: 26214
      atime3: 42598
      atime4: 65535
      atime5: 65535
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 3
      m_NumAlphaKeys: 5
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_SelectHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.5
    m_AdjustWidth: 1
    m_StarWidth: 0.003
    m_EndWidth: 0.003
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 0.5686275, g: 0.78431374, b: 1, a: 0.627451}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 1, g: 1, b: 1, a: 0.78431374}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 32768
      ctime2: 65535
      ctime3: 65535
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8192
      atime2: 26214
      atime3: 42598
      atime4: 65535
      atime5: 65535
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 3
      m_NumAlphaKeys: 5
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.75
  m_HoverHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.25
    m_AdjustWidth: 1
    m_StarWidth: 0.004
    m_EndWidth: 0.004
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 1, g: 0.78431374, b: 0.5686275, a: 0.49019608}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 1, g: 1, b: 1, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 16384
      ctime2: 65535
      ctime3: 65535
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 13878
      atime2: 32768
      atime3: 65535
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 3
      m_NumAlphaKeys: 4
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_RenderLineInWorldSpace: 1
  m_SwapMaterials: 0
  m_BaseLineMaterial: {fileID: 0}
  m_EmptyHitMaterial: {fileID: 0}
--- !u!222 &3730042378119755578
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2083018880704276259}
  m_CullTransparentMesh: 1
--- !u!1 &3731247915614967799
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7095667580457311747}
  - component: {fileID: 7837770898695884418}
  - component: {fileID: 8399808866277470502}
  - component: {fileID: 6275824269102874826}
  - component: {fileID: 1899011120009701176}
  m_Layer: 0
  m_Name: VR_Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!1 &3754479819261742745
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6097894791315742696}
  - component: {fileID: 2645001781349080075}
  m_Layer: 0
  m_Name: RequestPermission
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3758277795233954449
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1786676441828673123}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8943621866000577906}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &3758513638636659021
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5255911528893758561}
  - component: {fileID: 5137339078902365848}
  m_Layer: 0
  m_Name: avaturn_body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3767527757336721719
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4004642021144670011}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2620991336296125861}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3788156388887934598
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1228367496959184203}
  m_Layer: 0
  m_Name: LeftArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3797029478118711239
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 3.002, y: 0, z: -981}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 496353250390962969}
  - {fileID: 4285635575700484274}
  - {fileID: 8473767348284954952}
  - {fileID: 3984814576672651556}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &3806786343414362346
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8538758053982546435}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5079801, y: 0.5056839, z: -0.495239, w: 0.49089557}
  m_LocalPosition: {x: 0.7104411, y: 1.5039446, z: -0.04624611}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5347058672833980954}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &3813226075777540303
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7778125754123030126}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 12ed28e894f164b4ebe161f47489fadb, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &3827930346453364790
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4109662309346090169}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000013985816, y: -0.1305262, z: 0.000000018412676, w: 0.9914449}
  m_LocalPosition: {x: -0.0125, y: -0.028556997, z: 0.027326612}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 318914851436607795}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3850661809983654175
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2607784414646659857}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f425ef7b9f6441f4aac2758f3630b425, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &3853495253572125805
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3506797319206247025}
  - component: {fileID: 8035523432992933076}
  m_Layer: 0
  m_Name: DrawAvatarInfo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3867202766007674733
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6238845496079627304}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4409373711675685735}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &3876710783076775415
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2788367612727919741}
  m_Layer: 0
  m_Name: RightHandRing2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3878823456568567506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8112452301063554017}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00042568, y: -0.013497433, z: -0.0000918817, w: 0.9999088}
  m_LocalPosition: {x: -0.000000028303266, y: 0.021747319, z: -0.00000026969252}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7322571774275400544}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3885301077535107034
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6142710786534630359}
  - component: {fileID: 3196501066792115639}
  - component: {fileID: 823299636642840592}
  m_Layer: 0
  m_Name: ThumbStick_Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &3886752523380519911
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2947483358204636977}
  m_Layer: 0
  m_Name: RightHandThumb3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &3896318289523821966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5303906344900895163}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01f69dc1cb084aa42b2f2f8cd87bc770, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 7739599882840324738}
  m_TransformationPriority: 0
  m_DelayTime: 0
--- !u!114 &3954076349469409366
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3988972325260091870}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10913, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!4 &3984814576672651556
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6743486549747732338}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 5.4539995, y: 0.28, z: -14.622002}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3797029478118711239}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3988972325260091870
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3304550752943941888}
  - component: {fileID: 6289198565412781368}
  - component: {fileID: 3954076349469409366}
  m_Layer: 0
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3993044746207940765
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2511277965148420841}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7104329100137566756}
  - {fileID: 4158078768350670396}
  m_Father: {fileID: 3446222053752739363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4004642021144670011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3767527757336721719}
  - component: {fileID: 7090847028827831956}
  m_Layer: 0
  m_Name: avaturn_shoes_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &4025994208654091527
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5902537773636369907}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &4026006277366733104
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5553491692786752903}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1959705464501603054}
  - {fileID: 1101418946447317244}
  m_Father: {fileID: 3446222053752739363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4057741426029735887
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6432746359520744335}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.8470589, g: 0.9607844, b: 0.38823533, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 3750c318f40cab44a97d3a81beaefeb3, type: 3}
    m_FontSize: 56
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 0
    m_MaxSize: 256
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Show Avaturn
--- !u!114 &4103289382005180883
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8247737509939603634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 600, y: 800}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0.683
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!1 &4105869907717761748
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2341674191280902196}
  m_Layer: 0
  m_Name: Handle Slide Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &4109662309346090169
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3827930346453364790}
  - component: {fileID: 7025305998219498638}
  - component: {fileID: 5848462624975853007}
  m_Layer: 0
  m_Name: Bumper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!137 &4120958361270886530
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4657654078849255502}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 6540941884980302035, guid: c2ca2d7a8cc7e344285ab81de5769515, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7506380042844687028, guid: c2ca2d7a8cc7e344285ab81de5769515, type: 3}
  m_Bones:
  - {fileID: 4975108577388796292}
  - {fileID: 7271366320287254483}
  - {fileID: 8194709199298547771}
  - {fileID: 3421309572655730664}
  - {fileID: 559369952382032964}
  - {fileID: 6223613710194887899}
  - {fileID: 4391677642099535108}
  - {fileID: 1228367496959184203}
  - {fileID: 715742585284539564}
  - {fileID: 7577900487402064988}
  - {fileID: 3155363803605568173}
  - {fileID: 6643006642981370824}
  - {fileID: 6798434318218008223}
  - {fileID: 4386289235407016483}
  - {fileID: 634068311700983852}
  - {fileID: 2808095484452820845}
  - {fileID: 7421115535572327446}
  - {fileID: 692624218676177310}
  - {fileID: 6606507566438310603}
  - {fileID: 5666268692752496191}
  - {fileID: 7322571774275400544}
  - {fileID: 3878823456568567506}
  - {fileID: 6596787623609719101}
  - {fileID: 2749870845763744358}
  - {fileID: 6259267943173207598}
  - {fileID: 8978555451093852455}
  - {fileID: 7334807809387987209}
  - {fileID: 8517523847440643472}
  - {fileID: 279653332813315513}
  - {fileID: 4400452983571757765}
  - {fileID: 4380871466541375476}
  - {fileID: 2947483358204636977}
  - {fileID: 5777818085006005823}
  - {fileID: 1286356308969779788}
  - {fileID: 3516606857722637648}
  - {fileID: 5911338096449038408}
  - {fileID: 3447113454583995759}
  - {fileID: 7161254564539608682}
  - {fileID: 1460506307876685992}
  - {fileID: 2788367612727919741}
  - {fileID: 1089412022518228957}
  - {fileID: 5222654588366059119}
  - {fileID: 6110409109699714452}
  - {fileID: 5664199988977456258}
  - {fileID: 2333636119849558983}
  - {fileID: 5061350368787562714}
  - {fileID: 332667422860186626}
  - {fileID: 8974198899552680212}
  - {fileID: 883269954257450315}
  - {fileID: 8023833221090823321}
  - {fileID: 9000211819660584494}
  - {fileID: 1294047678815427870}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4975108577388796292}
  m_AABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_DirtyAABB: 0
--- !u!1 &4156096697780834924
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 634068311700983852}
  m_Layer: 0
  m_Name: LeftHandIndex2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4158078768350670396
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5823867332776224558}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.085706234, y: 0.49550968, z: 0.84800136}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3993044746207940765}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &4167201772481382799
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433312847804040521}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.05
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &4186497871359179949
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7780604994006172390}
  - component: {fileID: 4809233297614281347}
  - component: {fileID: 7348354011957679096}
  m_Layer: 0
  m_Name: ThumbStick
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &4188622833522522867
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4432447139325270252}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1344c3c82d62a2a41a3576d8abb8e3ea, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Texture: {fileID: 0}
  m_UVRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
--- !u!4 &4248990946545048892
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1270683876034729651}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 535261126972462885}
  m_Father: {fileID: 496353250390962969}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &4252674557745911015
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6117099419200726635}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &4268915084993462964
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1796092491595218133}
  - component: {fileID: 6739654394357090568}
  - component: {fileID: 2481944593839669219}
  m_Layer: 0
  m_Name: Trigger
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &4278458331101851043
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8978555451093852455}
  m_Layer: 0
  m_Name: RightShoulder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!137 &4278881197966947620
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2271816024791054615}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: f5ccd52dc494e054fbe7d7161dcabe25, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -5120762275383614272, guid: e053b8fbc416ba349b4a58a26410bba2, type: 3}
  m_Bones: []
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 0}
  m_AABB:
    m_Center: {x: 0, y: 0.009045093, z: 0}
    m_Extent: {x: 0.0077457884, y: 0.016694028, z: 0.0077457884}
  m_DirtyAABB: 0
--- !u!4 &4285635575700484274
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5610636252869755943}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.9, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8506148699809443934}
  - {fileID: 3446222053752739363}
  - {fileID: 7095667580457311747}
  - {fileID: 8943621866000577906}
  - {fileID: 7851588616201146884}
  - {fileID: 293992037062236184}
  m_Father: {fileID: 3797029478118711239}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4297795886648814908
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4613608991934880773}
  - component: {fileID: 4879764860342710102}
  m_Layer: 2
  m_Name: Climb
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &4318703313496157382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5990502202272607132}
  - component: {fileID: 2703183126586657928}
  - component: {fileID: 8526549258102202459}
  m_Layer: 0
  m_Name: Button_Home
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &4338852539094006615
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6743486549747732338}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 83e4e6cca11330d4088d729ab4fc9d9f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
--- !u!1 &4349123533437619100
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6427239352650257207}
  - component: {fileID: 8574657644153347715}
  - component: {fileID: 6405450116479225832}
  m_Layer: 0
  m_Name: ThumbStick_Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &4380271565080752509
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8974198899552680212}
  m_Layer: 0
  m_Name: LeftToeBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4380871466541375476
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4754241270009363432}
  serializedVersion: 2
  m_LocalRotation: {x: 0.046991102, y: -0.022841817, z: -0.27797115, w: 0.9591674}
  m_LocalPosition: {x: -0.0000000030292102, y: 0.028347537, z: 0.0000002301244}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2947483358204636977}
  m_Father: {fileID: 4400452983571757765}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &4386289235407016483
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5394599040921417610}
  serializedVersion: 2
  m_LocalRotation: {x: 0.001770729, y: -0.0063213776, z: -0.05965166, w: 0.9981977}
  m_LocalPosition: {x: 0.020802997, y: 0.08079123, z: 0.0019605632}
  m_LocalScale: {x: 0.99999994, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 634068311700983852}
  m_Father: {fileID: 7577900487402064988}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4390012197431013410
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8695576789228953066}
  - component: {fileID: 7992926882691089504}
  - component: {fileID: 3424102171925712838}
  - component: {fileID: 6657430115615426907}
  - component: {fileID: 1838482847470871544}
  m_Layer: 0
  m_Name: Main Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4391677642099535108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6275374624156162446}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5298264, y: -0.5298264, z: 0.4682777, w: 0.46827766}
  m_LocalPosition: {x: -0.052188158, y: 0.16465577, z: 0.009380921}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1228367496959184203}
  m_Father: {fileID: 3421309572655730664}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &4400452983571757765
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6610317430992866894}
  serializedVersion: 2
  m_LocalRotation: {x: 0.15590715, y: -0.09437984, z: 0.39164874, w: 0.9018851}
  m_LocalPosition: {x: -0.020659894, y: 0.03584238, z: 0.01422533}
  m_LocalScale: {x: 1, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4380871466541375476}
  m_Father: {fileID: 279653332813315513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!224 &4409373711675685735
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 71643286533970796}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3867202766007674733}
  m_Father: {fileID: 7095667580457311747}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 160, y: 30}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &4432447139325270252
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 194626214125372492}
  - component: {fileID: 7391560311073942447}
  - component: {fileID: 4188622833522522867}
  - component: {fileID: 7656257333935239958}
  m_Layer: 5
  m_Name: VideoLayer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4452282283316580681
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2150014697992355620}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0075}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5356247828354287878}
  m_Father: {fileID: 7337106021779240675}
  m_LocalEulerAnglesHint: {x: 0, y: 10, z: 0}
--- !u!114 &4469309135895020567
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5807584919204290509}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e766f86cb7d2461683eb37d8a971fb14, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ClimbTeleportInteractor: {fileID: 7520960489843355295}
  m_PointerPrefab: {fileID: 5212361887338514247, guid: ae1968658b9687b47976fe86c062168f,
    type: 3}
  m_PointerDistance: 0.3
--- !u!137 &4491151500492688077
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5986276381935346121}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 5654804321838842103, guid: c2ca2d7a8cc7e344285ab81de5769515, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -4909680059550880528, guid: c2ca2d7a8cc7e344285ab81de5769515, type: 3}
  m_Bones:
  - {fileID: 4975108577388796292}
  - {fileID: 7271366320287254483}
  - {fileID: 8194709199298547771}
  - {fileID: 3421309572655730664}
  - {fileID: 559369952382032964}
  - {fileID: 6223613710194887899}
  - {fileID: 4391677642099535108}
  - {fileID: 1228367496959184203}
  - {fileID: 715742585284539564}
  - {fileID: 7577900487402064988}
  - {fileID: 3155363803605568173}
  - {fileID: 6643006642981370824}
  - {fileID: 6798434318218008223}
  - {fileID: 4386289235407016483}
  - {fileID: 634068311700983852}
  - {fileID: 2808095484452820845}
  - {fileID: 7421115535572327446}
  - {fileID: 692624218676177310}
  - {fileID: 6606507566438310603}
  - {fileID: 5666268692752496191}
  - {fileID: 7322571774275400544}
  - {fileID: 3878823456568567506}
  - {fileID: 6596787623609719101}
  - {fileID: 2749870845763744358}
  - {fileID: 6259267943173207598}
  - {fileID: 8978555451093852455}
  - {fileID: 7334807809387987209}
  - {fileID: 8517523847440643472}
  - {fileID: 279653332813315513}
  - {fileID: 4400452983571757765}
  - {fileID: 4380871466541375476}
  - {fileID: 2947483358204636977}
  - {fileID: 5777818085006005823}
  - {fileID: 1286356308969779788}
  - {fileID: 3516606857722637648}
  - {fileID: 5911338096449038408}
  - {fileID: 3447113454583995759}
  - {fileID: 7161254564539608682}
  - {fileID: 1460506307876685992}
  - {fileID: 2788367612727919741}
  - {fileID: 1089412022518228957}
  - {fileID: 5222654588366059119}
  - {fileID: 6110409109699714452}
  - {fileID: 5664199988977456258}
  - {fileID: 2333636119849558983}
  - {fileID: 5061350368787562714}
  - {fileID: 332667422860186626}
  - {fileID: 8974198899552680212}
  - {fileID: 883269954257450315}
  - {fileID: 8023833221090823321}
  - {fileID: 9000211819660584494}
  - {fileID: 1294047678815427870}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4975108577388796292}
  m_AABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_DirtyAABB: 0
--- !u!114 &4509087775236993182
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8443053389285184463}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6803edce0201f574f923fd9d10e5b30a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 2147483648
  m_Handedness: 2
  m_AttachTransform: {fileID: 1650976106500663011}
  m_KeepSelectedTargetValid: 0
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 36843f28-4fd5-4729-b5a6-afe92ef11597
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 1a51c331-470d-4462-b8e1-2522a24bd40c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: -8061240218431744966, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: -8061240218431744966, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 0ace7244-e61f-4e60-8d0b-2ef8c3ae51af
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: bdf06a24-21b3-4f27-a8a3-72086e6c7f00
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 83097790271614945, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: 7904272356298805229, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 0
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 0
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 0
  m_LineType: 1
  m_BlendVisualLinePoints: 1
  m_MaxRaycastDistance: 30
  m_RayOriginTransform: {fileID: 6291295526486202380}
  m_ReferenceFrame: {fileID: 0}
  m_Velocity: 10
  m_Acceleration: 9.8
  m_AdditionalGroundHeight: 0.1
  m_AdditionalFlightTime: 0.5
  m_EndPointDistance: 30
  m_EndPointHeight: -10
  m_ControlPointDistance: 10
  m_ControlPointHeight: 5
  m_SampleFrequency: 50
  m_HitDetectionType: 0
  m_SphereCastRadius: 0.1
  m_ConeCastAngle: 6
  m_LiveConeCastDebugVisuals: 0
  m_RaycastMask:
    serializedVersion: 2
    m_Bits: 2147483681
  m_RaycastTriggerInteraction: 1
  m_RaycastSnapVolumeInteraction: 1
  m_HitClosestOnly: 1
  m_HoverToSelect: 0
  m_HoverTimeToSelect: 0.5
  m_AutoDeselect: 0
  m_TimeToAutoDeselect: 1
  m_EnableUIInteraction: 0
  m_BlockInteractionsWithScreenSpaceUI: 0
  m_BlockUIOnInteractableSelection: 1
  m_ManipulateAttachTransform: 1
  m_UseForceGrab: 0
  m_RotateSpeed: 180
  m_TranslateSpeed: 0
  m_RotateReferenceFrame: {fileID: 0}
  m_RotateMode: 1
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_EnableARRaycasting: 0
  m_OccludeARHitsWith3DObjects: 0
  m_OccludeARHitsWith2DObjects: 0
  m_ScaleMode: 0
  m_UIPressInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 54867c8e-3650-4605-a53c-ee8ffb351dcf
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 257d8673-0295-4ff5-b278-e63d20cd918b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 8c6b1aac-a242-4bf4-a5b3-bfad6e83b638
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_TranslateManipulationInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Translate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 0f9fd0ee-650d-41a6-ab30-2a036c425c21
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RotateManipulationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Rotate Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: cadca2d2-f642-4efc-a222-c1827be3e896
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -5913262927076077117, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_DirectionalManipulationInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Directional Manipulation
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 472cbca3-7add-47a9-a5fc-73d3d10107aa
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -440298646266941818, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleToggleInput:
    m_InputSourceMode: 0
    m_InputActionPerformed:
      m_Name: Scale Toggle
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 692a9304-a2fd-4dbd-9e2f-2fb4b6154f1c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Scale Toggle Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: d0cf082b-f2d7-4100-b069-651cf2820425
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ScaleOverTimeInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Over Time
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 6f835f0d-f1c2-461c-b8bc-edc587e89149
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_ScaleDistanceDeltaInput:
    m_InputSourceMode: 0
    m_InputAction:
      m_Name: Scale Distance Delta
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 9140e1d5-f197-46d4-88c2-a02441edeac5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
--- !u!114 &4511218935134170365
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 484607128982752228}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 80e353695beb436ab39a90d9ecefaee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LineRenderer: {fileID: 2694127015239756176}
  m_CurveVisualObject: {fileID: 1062557283441490215}
  m_OverrideLineOrigin: 1
  m_LineOriginTransform: {fileID: 6327557944332572447}
  m_VisualPointCount: 20
  m_MaxVisualCurveDistance: 10
  m_RestingVisualLineLength: 0.25
  m_LineDynamicsMode: 1
  m_RetractDelay: 1
  m_RetractDuration: 1
  m_ExtendLineToEmptyHit: 0
  m_ExtensionRate: 10
  m_EndPointExpansionRate: 10
  m_ComputeMidPointWithComplexCurves: 0
  m_SnapToSelectedAttachIfAvailable: 1
  m_SnapToSnapVolumeIfAvailable: 1
  m_CurveStartOffset: 0.015
  m_CurveEndOffset: 0.005
  m_CustomizeLinePropertiesForState: 1
  m_LinePropertyAnimationSpeed: 8
  m_NoValidHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.25
    m_AdjustWidth: 1
    m_StarWidth: 0.003
    m_EndWidth: 0.003
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 1, g: 1, b: 1, a: 0.5019608}
      key2: {r: 0, g: 0, b: 0, a: 0.2509804}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 193
      atime1: 8192
      atime2: 32768
      atime3: 55705
      atime4: 65342
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 2
      m_NumAlphaKeys: 4
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_UIHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.5
    m_AdjustWidth: 1
    m_StarWidth: 0.004
    m_EndWidth: 0.004
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 1, g: 0.78431374, b: 0.5686275, a: 0.49019608}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 16384
      ctime2: 65535
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8192
      atime2: 32768
      atime3: 65535
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 3
      m_NumAlphaKeys: 4
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_UIPressHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.5
    m_AdjustWidth: 1
    m_StarWidth: 0.003
    m_EndWidth: 0.003
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 0.5686275, g: 0.78431374, b: 1, a: 0.627451}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 1, g: 1, b: 1, a: 0.78431374}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 32768
      ctime2: 65535
      ctime3: 65535
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8192
      atime2: 26214
      atime3: 42598
      atime4: 65535
      atime5: 65535
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 3
      m_NumAlphaKeys: 5
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_SelectHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.5
    m_AdjustWidth: 1
    m_StarWidth: 0.003
    m_EndWidth: 0.003
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 0.5686275, g: 0.78431374, b: 1, a: 0.627451}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 1, g: 1, b: 1, a: 0.78431374}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 32768
      ctime2: 65535
      ctime3: 65535
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 8192
      atime2: 26214
      atime3: 42598
      atime4: 65535
      atime5: 65535
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 3
      m_NumAlphaKeys: 5
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.75
  m_HoverHitProperties:
    m_SmoothlyCurveLine: 1
    m_LineBendRatio: 0.25
    m_AdjustWidth: 1
    m_StarWidth: 0.004
    m_EndWidth: 0.004
    m_EndWidthScaleDistanceFactor: 2
    m_AdjustGradient: 1
    m_Gradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 0}
      key1: {r: 1, g: 0.78431374, b: 0.5686275, a: 0.49019608}
      key2: {r: 1, g: 1, b: 1, a: 1}
      key3: {r: 1, g: 1, b: 1, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 16384
      ctime2: 65535
      ctime3: 65535
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 13878
      atime2: 32768
      atime3: 65535
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 3
      m_NumAlphaKeys: 4
    m_CustomizeExpandLineDrawPercent: 1
    m_ExpandModeLineDrawPercent: 0.9
  m_RenderLineInWorldSpace: 1
  m_SwapMaterials: 0
  m_BaseLineMaterial: {fileID: 0}
  m_EmptyHitMaterial: {fileID: 0}
--- !u!4 &4514296614285512320
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1868339462333439342}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6868920332256386382}
  m_Father: {fileID: 3446222053752739363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4526834763291727596
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1117558224355886537}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 75b29b6c6428c984a8a73ffc2d58063b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 7739599882840324738}
  m_TransformationPriority: 0
  m_TurnSpeed: 60
  m_LeftHandTurnInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Left Hand Turn
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 3610965d-108d-4451-a143-a78d1ee8f9b8
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 1010738217276881514, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RightHandTurnInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Right Hand Turn
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: eeb82678-2af4-4b6c-87fc-621bb707edc5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -6493913391331992944, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
--- !u!114 &4545211012869705450
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9b1e8c997df241c1a67045eeac79b41b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 5916155045363674533}
  m_TransformationPriority: 0
  m_MoveSpeed: 3
  m_EnableStrafe: 1
  m_EnableFly: 0
  m_UseGravity: 1
  m_ForwardSource: {fileID: 8695576789228953066}
  m_LeftHandMoveInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Left Hand Move
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 704612d9-96c4-4310-817b-26f85cdc5c9f
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RightHandMoveInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Right Hand Move
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: b3fc0bbd-ac2b-4cea-ab81-fdf8aeef9722
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_HeadTransform: {fileID: 8695576789228953066}
  m_LeftControllerTransform: {fileID: 6706157922200873912}
  m_RightControllerTransform: {fileID: 545667201402663227}
  m_LeftHandMovementDirection: 0
  m_RightHandMovementDirection: 0
--- !u!1 &4545920090398020782
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5113036621483924086}
  - component: {fileID: 6931096129579237216}
  - component: {fileID: 749406897334760467}
  - component: {fileID: 6121246629913202168}
  m_Layer: 0
  m_Name: Poke
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &4566592677701588093
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6123179565880450011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86d13fca2ec430d870c0f7765ad0dde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 6083702335336276294}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: idle
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hovered
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hoveredPriority
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: selected
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: activated
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: focused
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      m_ColorBlendMode: 0
      m_BlendAmount: 1
    m_Variable: {fileID: 11400000, guid: fc690d1505c48cb4696838b71abd2ca0, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 7095218367804858521}
  m_ColorPropertyName: _BaseColor
--- !u!114 &4574441567135131192
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7286427651859038901}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6ef0e4723b64c884699a375196c13ac0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FallbackIfEyeTrackingUnavailable: 1
--- !u!114 &4574743199282298143
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1072080526943758045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 61498d7f708724f568add451bdb149c9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactor: {fileID: 0}
  chipSelectionManager: {fileID: 8923731902986500387}
  targetGameObjectTag: Chip
--- !u!114 &4610631522096466495
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433312847804040521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c2fadf230d1919748a9aa21d40f74619, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackingType: 0
  m_UpdateType: 0
  m_IgnoreTrackingState: 0
  m_PositionInput:
    m_UseReference: 1
    m_Action:
      m_Name: Position
      m_Type: 0
      m_ExpectedControlType: Vector3
      m_Id: cd22b81e-c39a-4170-bdbf-33b5a06ea86f
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: -2024308242397127297, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_RotationInput:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation
      m_Type: 0
      m_ExpectedControlType: Quaternion
      m_Id: 04fa80c1-7876-441c-8416-c5ea3caea5c4
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 8248158260566104461, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_TrackingStateInput:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: fdcc0d62-5cd0-4fcc-8c3a-c07cf6230b7d
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 684395432459739428, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_PositionAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: ddb05e9d-4218-401c-a7a8-003424f4b4fa
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  m_RotationAction:
    m_Name: 
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: de98cdb6-b2c4-4d67-af3c-9c8bd60b295a
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
--- !u!4 &4613608991934880773
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4297795886648814908}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2741029711676812753}
  m_Father: {fileID: 8473767348284954952}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4620685079338242643
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7778125754123030126}
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &4635425379091036855
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 375204505576286140}
  - component: {fileID: 2154573498142499198}
  m_Layer: 0
  m_Name: UniWebView
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &4657654078849255502
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3554821131344161157}
  - component: {fileID: 4120958361270886530}
  m_Layer: 0
  m_Name: avaturn_look_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &4671199710004930166
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3797029478118711239}
  - component: {fileID: 8171373516390849214}
  - component: {fileID: 1729026098378324245}
  - component: {fileID: 5916155045363674533}
  - component: {fileID: 4545211012869705450}
  - component: {fileID: 6668217725418410777}
  - component: {fileID: 6915648343438824661}
  - component: {fileID: 7995985868530763277}
  - component: {fileID: 867021761789850697}
  - component: {fileID: 1839183715752652042}
  - component: {fileID: 7927393120081356902}
  - component: {fileID: 8801252366402239759}
  - component: {fileID: 7781576453842849366}
  - component: {fileID: 5611976146319052605}
  - component: {fileID: 2448003236372656183}
  - component: {fileID: 453748873995594232}
  - component: {fileID: 8945399097114654071}
  - component: {fileID: 8923731902986500387}
  m_Layer: 2
  m_Name: XR Origin (XR Rig) (3.0 Test)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &4683310073714609297
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7941259143105206269}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5600854154165873622}
  - {fileID: 475444165889946360}
  m_Father: {fileID: 7337106021779240675}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4691006411662421713
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2995845308996088401}
  - component: {fileID: 7899463311402586077}
  - component: {fileID: 1454032667787034614}
  - component: {fileID: 8083315982671937065}
  - component: {fileID: 1642833139858708784}
  m_Layer: 5
  m_Name: CanvasWebViewPrefabView
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!210 &4695113682247617013
SortingGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3652913999594940763}
  m_Enabled: 1
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 30005
  m_SortAtRoot: 0
--- !u!1 &4715002704412539578
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5911338096449038408}
  m_Layer: 0
  m_Name: RightHandMiddle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &4744729318664804077
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3032641275601302522}
  - component: {fileID: 6416779409004539956}
  m_Layer: 5
  m_Name: WebViewFrame
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4751113485835488039
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1117558224355886537}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8473767348284954952}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4754241270009363432
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4380871466541375476}
  m_Layer: 0
  m_Name: RightHandThumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &4757578893115389222
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 895014221006676911}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 47f69f0ec367e4e75bfded9658c9851e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  minRotAngle: -30
  maxRotAngle: 60
  headVRTransform: {fileID: 8695576789228953066}
  cameraOffset: {fileID: 0}
  headRotText: {fileID: 57552291714031914}
--- !u!114 &4764869234962504907
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1546219791638165760}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!4 &4783094969730892873
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044456232784710588}
  serializedVersion: 2
  m_LocalRotation: {x: 0.18379451, y: -0.00000008593347, z: 0.000000016067828, w: 0.9829647}
  m_LocalPosition: {x: 0.0000000071757764, y: -0.0032368493, z: 0.024549427}
  m_LocalScale: {x: 1.01935, y: 1.01935, z: 1.01935}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2072851329618579022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4809233297614281347
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4186497871359179949}
  m_Mesh: {fileID: -2564423107879867638, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!1 &4829954178147579237
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5477248990418324633}
  - component: {fileID: 7698002628919082017}
  - component: {fileID: 5929130879585830546}
  m_Layer: 0
  m_Name: Button_B
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &4848154710275565594
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4916089409233462726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &4879764860342710102
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4297795886648814908}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 496880615cd240be960d436c1c8ae570, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 7739599882840324738}
  m_TransformationPriority: 0
  m_ClimbSettings:
    m_UseConstant: 1
    m_ConstantValue:
      m_AllowFreeXMovement: 1
      m_AllowFreeYMovement: 1
      m_AllowFreeZMovement: 1
    m_Variable: {fileID: 0}
--- !u!1 &4916089409233462726
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8943621866000577906}
  - component: {fileID: 5844310847419289193}
  - component: {fileID: 3067972690501691380}
  - component: {fileID: 4848154710275565594}
  - component: {fileID: 8924248762193192422}
  m_Layer: 0
  m_Name: Avaturn_VR_Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &4919935681502709809
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1521370347279902152}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.56344235, y: 0.56344235, z: 0.56344235}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7062042876505683019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &4940270895843040609
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5160961464378193121}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b781fe673a5534e91b1e802df4b9362e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  importSettings:
    nodeNameMethod: 0
    animationMethod: 1
    generateMipMaps: 1
    defaultMinFilterMode: 0
    defaultMagFilterMode: 0
    anisotropicFilterLevel: 4
  url: 
  loadOnStartup: 0
  sceneId: -1
  playAutomatically: 1
  streamingAsset: 0
  instantiationSettings:
    mask: -1
    layer: 0
    skinUpdateWhenOffscreen: 1
    lightIntensityFactor: 1
    sceneObjectCreation: 2
--- !u!4 &4975108577388796292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7425416499237917644}
  serializedVersion: 2
  m_LocalRotation: {x: -0.004158379, y: -0.000000075940676, z: 3.157902e-10, w: 0.99999136}
  m_LocalPosition: {x: 4.6930006e-17, y: 0.96677476, z: 0.0140593285}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2333636119849558983}
  - {fileID: 883269954257450315}
  - {fileID: 7271366320287254483}
  m_Father: {fileID: 2620991336296125861}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5047964463645859763
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8517523847440643472}
  m_Layer: 0
  m_Name: RightForeArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5061350368787562714
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6551049527177071160}
  serializedVersion: 2
  m_LocalRotation: {x: -0.043966834, y: 0.0003047775, z: -0.000013390967, w: 0.999033}
  m_LocalPosition: {x: -8.662798e-10, y: 0.41549367, z: 0.0000000021294293}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 332667422860186626}
  m_Father: {fileID: 2333636119849558983}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!120 &5072605204120713644
LineRenderer:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8443053389285184463}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10306, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions: []
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.01
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 4
    numCapVertices: 4
    alignment: 0
    textureMode: 0
    textureScale: {x: 1, y: 1}
    shadowBias: 0.5
    generateLightingData: 0
  m_MaskInteraction: 0
  m_UseWorldSpace: 1
  m_Loop: 0
  m_ApplyActiveColorSpace: 0
--- !u!114 &5098067480823488016
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8247737509939603634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7951c64acb0fa62458bf30a60089fe2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 0
  m_CheckFor2DOcclusion: 0
  m_CheckFor3DOcclusion: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RaycastTriggerInteraction: 1
--- !u!4 &5099228242498882035
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7778125754123030126}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.02}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8695576789228953066}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5111301790308015182
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6536985365452920497}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c83f12c537584f51b92c01f10d7090c0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransitionDuration: 0.125
  m_InteractorSource: {fileID: 1062557283441490215}
  m_IgnoreHoverEvents: 0
  m_IgnoreSelectEvents: 0
  m_IgnoreActivateEvents: 1
  m_IgnoreUGUIHover: 0
  m_IgnoreUGUISelect: 0
  m_IgnoreXRInteractionEvents: 0
  m_SelectClickAnimationMode: 1
  m_ActivateClickAnimationMode: 1
  m_ClickAnimationDuration: 0.25
  m_ClickAnimationCurve:
    m_UseConstant: 1
    m_ConstantValue:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_Variable: {fileID: 0}
--- !u!4 &5113036621483924086
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4545920090398020782}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5164021330966194647}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5118935179944215824
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 592124764034376607}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9b1e8c997df241c1a67045eeac79b41b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 7739599882840324738}
  m_TransformationPriority: 0
  m_MoveSpeed: 2.5
  m_EnableStrafe: 1
  m_EnableFly: 0
  m_UseGravity: 1
  m_ForwardSource: {fileID: 8695576789228953066}
  m_LeftHandMoveInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Left Hand Move
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 16c2fabb-fb1c-4a11-94d0-0b1d894b8593
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 6972639530819350904, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RightHandMoveInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Right Hand Move
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: af2e3d83-024e-4a1f-8bc1-f97f0b4ae1d5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8198699208435500284, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_HeadTransform: {fileID: 8695576789228953066}
  m_LeftControllerTransform: {fileID: 6706157922200873912}
  m_RightControllerTransform: {fileID: 545667201402663227}
  m_LeftHandMovementDirection: 0
  m_RightHandMovementDirection: 0
--- !u!137 &5137339078902365848
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3758513638636659021}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 3427393761193756682, guid: c2ca2d7a8cc7e344285ab81de5769515, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2147868319440173638, guid: c2ca2d7a8cc7e344285ab81de5769515, type: 3}
  m_Bones:
  - {fileID: 4975108577388796292}
  - {fileID: 7271366320287254483}
  - {fileID: 8194709199298547771}
  - {fileID: 3421309572655730664}
  - {fileID: 559369952382032964}
  - {fileID: 6223613710194887899}
  - {fileID: 4391677642099535108}
  - {fileID: 1228367496959184203}
  - {fileID: 715742585284539564}
  - {fileID: 7577900487402064988}
  - {fileID: 3155363803605568173}
  - {fileID: 6643006642981370824}
  - {fileID: 6798434318218008223}
  - {fileID: 4386289235407016483}
  - {fileID: 634068311700983852}
  - {fileID: 2808095484452820845}
  - {fileID: 7421115535572327446}
  - {fileID: 692624218676177310}
  - {fileID: 6606507566438310603}
  - {fileID: 5666268692752496191}
  - {fileID: 7322571774275400544}
  - {fileID: 3878823456568567506}
  - {fileID: 6596787623609719101}
  - {fileID: 2749870845763744358}
  - {fileID: 6259267943173207598}
  - {fileID: 8978555451093852455}
  - {fileID: 7334807809387987209}
  - {fileID: 8517523847440643472}
  - {fileID: 279653332813315513}
  - {fileID: 4400452983571757765}
  - {fileID: 4380871466541375476}
  - {fileID: 2947483358204636977}
  - {fileID: 5777818085006005823}
  - {fileID: 1286356308969779788}
  - {fileID: 3516606857722637648}
  - {fileID: 5911338096449038408}
  - {fileID: 3447113454583995759}
  - {fileID: 7161254564539608682}
  - {fileID: 1460506307876685992}
  - {fileID: 2788367612727919741}
  - {fileID: 1089412022518228957}
  - {fileID: 5222654588366059119}
  - {fileID: 6110409109699714452}
  - {fileID: 5664199988977456258}
  - {fileID: 2333636119849558983}
  - {fileID: 5061350368787562714}
  - {fileID: 332667422860186626}
  - {fileID: 8974198899552680212}
  - {fileID: 883269954257450315}
  - {fileID: 8023833221090823321}
  - {fileID: 9000211819660584494}
  - {fileID: 1294047678815427870}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4975108577388796292}
  m_AABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_DirtyAABB: 0
--- !u!1 &5160961464378193121
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2673107099597323512}
  - component: {fileID: 4940270895843040609}
  - component: {fileID: 1527898246755899815}
  m_Layer: 0
  m_Name: Loader
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5164021330966194647
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5425390600239528208}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5113036621483924086}
  - {fileID: 2420031529279389316}
  m_Father: {fileID: 6201949576961765177}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5184772924517249097
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9000211819660584494}
  m_Layer: 0
  m_Name: RightFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &5187423859371698993
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1072080526943758045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9ac216f0eb04754b1d938aac6380b31, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RayInteractor: {fileID: 0}
  m_NearFarInteractor: {fileID: 7812155368381269535}
  m_TeleportInteractor: {fileID: 4509087775236993182}
  m_TeleportMode: {fileID: -8061240218431744966, guid: c348712bda248c246b8c49b3db54643f,
    type: 3}
  m_TeleportModeCancel: {fileID: 2307464322626738743, guid: c348712bda248c246b8c49b3db54643f,
    type: 3}
  m_Turn: {fileID: -6493913391331992944, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_SnapTurn: {fileID: -8525429354371678379, guid: c348712bda248c246b8c49b3db54643f,
    type: 3}
  m_Move: {fileID: -8198699208435500284, guid: c348712bda248c246b8c49b3db54643f, type: 3}
  m_UIScroll: {fileID: -6756787485274679044, guid: c348712bda248c246b8c49b3db54643f,
    type: 3}
  m_SmoothMotionEnabled: 0
  m_SmoothTurnEnabled: 1
  m_NearFarEnableTeleportDuringNearInteraction: 1
  m_UIScrollingEnabled: 1
  m_RayInteractorChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &5191549424350030042
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5664199988977456258}
  m_Layer: 0
  m_Name: RightHandPinky3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5222654588366059119
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7245424946418628372}
  serializedVersion: 2
  m_LocalRotation: {x: 0.015946511, y: -0.0636294, z: -0.041950554, w: 0.996964}
  m_LocalPosition: {x: 0.035876404, y: 0.074265145, z: 0.0054223365}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6110409109699714452}
  m_Father: {fileID: 279653332813315513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5242014224930435635
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1508358508567715262}
  m_Layer: 0
  m_Name: XRController_Thumbstick_Buttons
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &5253460690420914100
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3381608978322424781}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &5255911528893758561
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3758513638636659021}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2620991336296125861}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &5296241919768237731
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1867588534553549969}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 9f12d299d16099343a3c5c0d7285822a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5303906344900895163
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3533296154301763878}
  - component: {fileID: 3896318289523821966}
  m_Layer: 2
  m_Name: Teleportation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &5325505344789907886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2011203541378567110}
  m_Layer: 0
  m_Name: Left Controller Stabilized Attach
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5347058672833980954
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7722944017648515500}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3806786343414362346}
  - {fileID: 2238439274622436440}
  m_Father: {fileID: 3446222053752739363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &5356247828354287878
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1788040821291060895}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -0.72}
  m_LocalScale: {x: 50, y: 50, z: 50}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4452282283316580681}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!1 &5394599040921417610
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4386289235407016483}
  m_Layer: 0
  m_Name: LeftHandIndex1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &5414809316943312770
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8247737509939603634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!1 &5425390600239528208
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5164021330966194647}
  m_Layer: 0
  m_Name: Poke Point Affordances
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5477248990418324633
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4829954178147579237}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.012, y: 0.0013757758, z: -0.009}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1508358508567715262}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5495894395949649344
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1441413611533425565}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &5499352854522851950
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 71643286533970796}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 2745325864379839978}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: Avaturn.Core.Runtime.Scripts.Mobile.MenuMobile,
          Avaturn.Core.Runtime
        m_MethodName: Open
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!1 &5553491692786752903
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4026006277366733104}
  - component: {fileID: 6728438529743207398}
  m_Layer: 0
  m_Name: Left_Leg_IK
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5600854154165873622
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6123179565880450011}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4683310073714609297}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5610636252869755943
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4285635575700484274}
  - component: {fileID: 5956166919948328918}
  - component: {fileID: 3354135046105739863}
  - component: {fileID: 5726604882724289091}
  - component: {fileID: 8154339635827155344}
  - component: {fileID: 409048068601817664}
  m_Layer: 0
  m_Name: Player_Root_Object
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &5611976146319052605
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 138336bec7c0f4772842cb66af5921fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &5664199988977456258
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5191549424350030042}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0021362908, y: -0.007985741, z: -0.0029569792, w: 0.9999615}
  m_LocalPosition: {x: 0.000000011056638, y: 0.018446665, z: -0.00000010216215}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6110409109699714452}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &5666268692752496191
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 317081877040910432}
  serializedVersion: 2
  m_LocalRotation: {x: 0.014314168, y: 0.04691892, z: 0.025612378, w: 0.9984677}
  m_LocalPosition: {x: -0.019884976, y: 0.08415061, z: 0.0011991345}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7322571774275400544}
  m_Father: {fileID: 7577900487402064988}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &5668317968300891774
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3374826707889493468}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.0387, y: 0.1911, z: 0.0418}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 279653332813315513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5672919205124789578
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8506148699809443934}
  - component: {fileID: 2307306175086723115}
  - component: {fileID: 365169162618499662}
  m_Layer: 0
  m_Name: Player
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!222 &5683243799015076350
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1689585148759238803}
  m_CullTransparentMesh: 1
--- !u!224 &5703946418305378629
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1441413611533425565}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7062042876505683019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!95 &5726604882724289091
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5610636252869755943}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 1720c98616a0f194dae551fe7645b49b, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!4 &5741279186860494031
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2829692256499286248}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 318914851436607795}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5765025255924269130
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1072080526943758045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4253f32900bcc4d499d675566142ded0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 4338852539094006615}
  m_InteractionLayers:
    m_Bits: 4294967295
  m_Handedness: 0
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: b8f2ac32-cd67-49f2-9f89-a87794b19684
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 6e078a95-ec6b-420c-ab26-cc5a10a77d65
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: a7dc4242-69e4-4565-97ea-0d07e339db75
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: c8e9b79a-1a87-41ae-a094-e6a5b56953bf
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 0}
    m_InputActionReferenceValue: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 1
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 1
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 1
  m_ImproveAccuracyWithSphereCollider: 0
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_PhysicsTriggerInteraction: 1
--- !u!4 &5777818085006005823
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3506104228170955532}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0017707318, y: 0.0063213776, z: 0.059651658, w: 0.9981977}
  m_LocalPosition: {x: -0.020802984, y: 0.080791265, z: 0.0019605977}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1286356308969779788}
  m_Father: {fileID: 279653332813315513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5807584919204290509
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2741029711676812753}
  - component: {fileID: 7520960489843355295}
  - component: {fileID: 4469309135895020567}
  m_Layer: 2
  m_Name: Climb Teleport
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &5823867332776224558
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4158078768350670396}
  m_Layer: 0
  m_Name: Right_Leg_IK_hint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &5828405131873502227
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6966779063830867726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd0b9921bce4eeb49bd05815b1135ac2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractorSourceObject: {fileID: 1062557283441490215}
  m_HapticImpulsePlayer: {fileID: 0}
  m_PlaySelectEntered: 1
  m_SelectEnteredData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectExited: 0
  m_SelectExitedData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectCanceled: 0
  m_SelectCanceledData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverEntered: 1
  m_HoverEnteredData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverExited: 0
  m_HoverExitedData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverCanceled: 0
  m_HoverCanceledData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_AllowHoverHapticsWhileSelecting: 0
--- !u!223 &5844310847419289193
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4916089409233462726}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!23 &5848462624975853007
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4109662309346090169}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &5849612435864072034
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2654499417665259012}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.042, y: 0.1703, z: 0.031}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7577900487402064988}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5882278563207976760
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8023833221090823321}
  m_Layer: 0
  m_Name: RightLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &5902537773636369907
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6165861432464125509}
  - component: {fileID: 8608027681157781872}
  - component: {fileID: 4025994208654091527}
  m_Layer: 0
  m_Name: Button_A
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5911338096449038408
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4715002704412539578}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0149332145, y: -0.030035648, z: 0.012575398, w: 0.9993582}
  m_LocalPosition: {x: 0.00026212694, y: 0.08388794, z: -0.00008437368}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3447113454583995759}
  m_Father: {fileID: 279653332813315513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5916155045363674533
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6fa7b4195685c3846be746c74f0ab2f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!23 &5929130879585830546
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4829954178147579237}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &5937777216826553390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3652913999594940763}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.02, z: -0.035}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6706157922200873912}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5956166919948328918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5610636252869755943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b2d8418b0b9634b1892b0268dd9c2743, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  boneShape: 1
  drawBones: 1
  drawTripods: 0
  boneSize: 1
  tripodSize: 1
  boneColor: {r: 0, g: 0, b: 1, a: 0.5}
  m_Transforms:
  - {fileID: 4285635575700484274}
  - {fileID: 8506148699809443934}
  - {fileID: 2620991336296125861}
  - {fileID: 4975108577388796292}
  - {fileID: 2333636119849558983}
  - {fileID: 5061350368787562714}
  - {fileID: 332667422860186626}
  - {fileID: 8974198899552680212}
  - {fileID: 883269954257450315}
  - {fileID: 8023833221090823321}
  - {fileID: 9000211819660584494}
  - {fileID: 1294047678815427870}
  - {fileID: 7271366320287254483}
  - {fileID: 8194709199298547771}
  - {fileID: 3421309572655730664}
  - {fileID: 559369952382032964}
  - {fileID: 6223613710194887899}
  - {fileID: 4391677642099535108}
  - {fileID: 1228367496959184203}
  - {fileID: 715742585284539564}
  - {fileID: 7577900487402064988}
  - {fileID: 4386289235407016483}
  - {fileID: 634068311700983852}
  - {fileID: 2808095484452820845}
  - {fileID: 7421115535572327446}
  - {fileID: 692624218676177310}
  - {fileID: 6606507566438310603}
  - {fileID: 6596787623609719101}
  - {fileID: 2749870845763744358}
  - {fileID: 6259267943173207598}
  - {fileID: 5666268692752496191}
  - {fileID: 7322571774275400544}
  - {fileID: 3878823456568567506}
  - {fileID: 3155363803605568173}
  - {fileID: 6643006642981370824}
  - {fileID: 6798434318218008223}
  - {fileID: 8978555451093852455}
  - {fileID: 7334807809387987209}
  - {fileID: 8517523847440643472}
  - {fileID: 279653332813315513}
  - {fileID: 5777818085006005823}
  - {fileID: 1286356308969779788}
  - {fileID: 3516606857722637648}
  - {fileID: 5911338096449038408}
  - {fileID: 3447113454583995759}
  - {fileID: 7161254564539608682}
  - {fileID: 5222654588366059119}
  - {fileID: 6110409109699714452}
  - {fileID: 5664199988977456258}
  - {fileID: 1460506307876685992}
  - {fileID: 2788367612727919741}
  - {fileID: 1089412022518228957}
  - {fileID: 4400452983571757765}
  - {fileID: 4380871466541375476}
  - {fileID: 2947483358204636977}
  - {fileID: 5255911528893758561}
  - {fileID: 8274526277103049002}
  - {fileID: 3767527757336721719}
  - {fileID: 3554821131344161157}
--- !u!1 &5986276381935346121
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8274526277103049002}
  - component: {fileID: 4491151500492688077}
  m_Layer: 0
  m_Name: avaturn_hair_0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5990502202272607132
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318703313496157382}
  serializedVersion: 2
  m_LocalRotation: {x: 0.18379451, y: -0.00000008593347, z: 0.000000016067828, w: 0.9829647}
  m_LocalPosition: {x: 0.0000000071757764, y: -0.0032368493, z: 0.024549427}
  m_LocalScale: {x: 1.01935, y: 1.01935, z: 1.01935}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 318914851436607795}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &5994736955204494484
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6115232339386362694}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.0075}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8761334942438416270}
  m_Father: {fileID: 6201949576961765177}
  m_LocalEulerAnglesHint: {x: 0, y: -10, z: 0}
--- !u!1 &6031087458372589770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2620991336296125861}
  m_Layer: 0
  m_Name: Armature
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &6083702335336276294
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6123179565880450011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c83f12c537584f51b92c01f10d7090c0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransitionDuration: 0.125
  m_InteractorSource: {fileID: 1676166793883116007}
  m_IgnoreHoverEvents: 0
  m_IgnoreSelectEvents: 0
  m_IgnoreActivateEvents: 1
  m_IgnoreUGUIHover: 0
  m_IgnoreUGUISelect: 0
  m_IgnoreXRInteractionEvents: 0
  m_SelectClickAnimationMode: 1
  m_ActivateClickAnimationMode: 1
  m_ClickAnimationDuration: 0.25
  m_ClickAnimationCurve:
    m_UseConstant: 1
    m_ConstantValue:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_Variable: {fileID: 0}
--- !u!1 &6090362364418762880
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3446222053752739363}
  - component: {fileID: 8904514290230835218}
  - component: {fileID: 6673519039911710674}
  m_Layer: 0
  m_Name: VR_IK_Constraints
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6097894791315742696
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3754479819261742745}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.4916826, y: 1.4916826, z: 1.4916826}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7062042876505683019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -540.735, y: -304.30325}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &6110409109699714452
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1236817472662357903}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00069869775, y: -0.010598074, z: -0.00049806485, w: 0.9999435}
  m_LocalPosition: {x: -0.0000000133237315, y: 0.02671358, z: 0.00000013091001}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5664199988977456258}
  m_Father: {fileID: 5222654588366059119}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6115232339386362694
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5994736955204494484}
  m_Layer: 0
  m_Name: Poke Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &6116528445589379180
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8324876636362646592}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6117099419200726635
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7052516765373991646}
  - component: {fileID: 6666113864808383841}
  - component: {fileID: 4252674557745911015}
  m_Layer: 0
  m_Name: TouchPad
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &6121246629913202168
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4545920090398020782}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86d13fca2ec430d870c0f7765ad0dde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 6931096129579237216}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: idle
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hovered
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hoveredPriority
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: selected
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: activated
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: focused
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      m_ColorBlendMode: 0
      m_BlendAmount: 1
    m_Variable: {fileID: 11400000, guid: fc690d1505c48cb4696838b71abd2ca0, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 749406897334760467}
  m_ColorPropertyName: _BaseColor
--- !u!1 &6123179565880450011
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5600854154165873622}
  - component: {fileID: 6083702335336276294}
  - component: {fileID: 7095218367804858521}
  - component: {fileID: 4566592677701588093}
  m_Layer: 0
  m_Name: Poke
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6137808655463954828
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6915321513614555395}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3086063312870280375}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 10, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &6141893153934801516
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6705022225288302881}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.012, y: 0.0013757758, z: -0.009}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2177334080802346132}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &6142710786534630359
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3885301077535107034}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000059604645, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.008775877, y: 0.00152745, z: -0.0074315914}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2177334080802346132}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &6165861432464125509
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5902537773636369907}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.007800013, y: 0.0013757758, z: 0.0055}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2177334080802346132}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &6201949576961765177
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2088670065357337757}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5994736955204494484}
  - {fileID: 5164021330966194647}
  m_Father: {fileID: 545667201402663227}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6206877780613868119
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1689585148759238803}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!4 &6223613710194887899
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 205920812168309993}
  serializedVersion: 2
  m_LocalRotation: {x: -0.112531625, y: -0.00000023252855, z: -0.0000000053046616,
    w: 0.9936482}
  m_LocalPosition: {x: -8.9213855e-15, y: 0.12000663, z: -0.0000000054329847}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 559369952382032964}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &6230530511651330972
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6705022225288302881}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6238845496079627304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3867202766007674733}
  - component: {fileID: 2336984865471353042}
  - component: {fileID: 8413157986821949054}
  m_Layer: 0
  m_Name: Text (Legacy)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6259267943173207598
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1696983009162425143}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0021362973, y: 0.007985748, z: 0.0029569878, w: 0.9999615}
  m_LocalPosition: {x: -0.000000038168274, y: 0.018446721, z: -0.000000063892315}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2749870845763744358}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6275374624156162446
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4391677642099535108}
  m_Layer: 0
  m_Name: LeftShoulder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &6275824269102874826
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3731247915614967799}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!222 &6289198565412781368
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3988972325260091870}
  m_CullTransparentMesh: 1
--- !u!4 &6291295526486202380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1249094729052492475}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1650976106500663011}
  m_Father: {fileID: 496353250390962969}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &************8853448
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8194709199298547771}
  m_Layer: 0
  m_Name: Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6327557944332572447
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6966779063830867726}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8169210416909721770}
  m_Father: {fileID: 6706157922200873912}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6338013453508777102
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6868920332256386382}
  m_Layer: 0
  m_Name: Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &6338921064379881376
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7778125754123030126}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7c2d507c3ae9dc744a28e79d568b5806, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fadeOnStart: 0
  fadeDuration: 3
  fadeColor: {r: 0, g: 0, b: 0, a: 1}
  fadeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 2
      outSlope: 2
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  colorPropertyName: _BaseColor
  PortalVFX: {fileID: 0}
--- !u!223 &6355024907514659822
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8247737509939603634}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 1
  m_TargetDisplay: 0
--- !u!120 &6361084006152553736
LineRenderer:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3652913999594940763}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10306, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions: []
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.01
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 0}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 0
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 4
    numCapVertices: 4
    alignment: 0
    textureMode: 0
    textureScale: {x: 1, y: 1}
    shadowBias: 0.5
    generateLightingData: 0
  m_MaskInteraction: 0
  m_UseWorldSpace: 1
  m_Loop: 0
  m_ApplyActiveColorSpace: 0
--- !u!1 &6367351471831352310
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7161254564539608682}
  m_Layer: 0
  m_Name: RightHandMiddle3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!222 &6386483533056644797
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2557475164967223925}
  m_CullTransparentMesh: 1
--- !u!114 &6393680207812758106
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1072080526943758045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b734f2bd29eeddd4d85afb0c266228c3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HapticOutput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Haptic
      m_Type: 2
      m_ExpectedControlType: 
      m_Id: b71b5bb4-1b09-415f-a4df-1476771fcae6
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8222252007134549311, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
  m_AmplitudeMultiplier: 1
--- !u!23 &6405450116479225832
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4349123533437619100}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!4 &6411585347646833231
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545711285861446478}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7265973089823045129}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!222 &6416779409004539956
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4744729318664804077}
  m_CullTransparentMesh: 1
--- !u!114 &6424885846941238709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3232823210907559783}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 3750c318f40cab44a97d3a81beaefeb3, type: 3}
    m_FontSize: 24
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 2
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Hide Avaturn
--- !u!4 &6427239352650257207
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4349123533437619100}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000059604645, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.008775877, y: 0.00152745, z: -0.0074315914}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1508358508567715262}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6432746359520744335
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6510763906158283465}
  - component: {fileID: 8110384688326307851}
  - component: {fileID: 4057741426029735887}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &6510763906158283465
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6432746359520744335}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8760523660980945685}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &6536985365452920497
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 475444165889946360}
  - component: {fileID: 5111301790308015182}
  - component: {fileID: 576178702574633366}
  - component: {fileID: 7587747077337991569}
  m_Layer: 0
  m_Name: NearFar
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &6551049527177071160
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5061350368787562714}
  m_Layer: 0
  m_Name: LeftLeg
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &6567829091087403004
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1868339462333439342}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8935e03361a14ec4b8edc872f76bc822, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Weight: 1
  m_Data:
    m_ConstrainedObject: {fileID: 6223613710194887899}
    m_SourceObjects:
      m_Length: 1
      m_Item0:
        transform: {fileID: 6868920332256386382}
        weight: 1
      m_Item1:
        transform: {fileID: 0}
        weight: 0
      m_Item2:
        transform: {fileID: 0}
        weight: 0
      m_Item3:
        transform: {fileID: 0}
        weight: 0
      m_Item4:
        transform: {fileID: 0}
        weight: 0
      m_Item5:
        transform: {fileID: 0}
        weight: 0
      m_Item6:
        transform: {fileID: 0}
        weight: 0
      m_Item7:
        transform: {fileID: 0}
        weight: 0
    m_ConstrainedPositionAxes:
      x: 1
      y: 1
      z: 1
    m_ConstrainedRotationAxes:
      x: 1
      y: 1
      z: 1
    m_MaintainPositionOffset: 0
    m_MaintainRotationOffset: 0
--- !u!4 &6572348054715713375
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 895014221006676911}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.82799983, y: 0.036159992, z: 0.878}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8695576789228953066}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!222 &6581999353183459650
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600680041059445812}
  m_CullTransparentMesh: 1
--- !u!114 &6589592714198776237
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848228539067598214}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8298a8a892733b9489040b7d1cb837a3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _webView: {fileID: 0}
--- !u!4 &6596787623609719101
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3551008720678513838}
  serializedVersion: 2
  m_LocalRotation: {x: 0.01594652, y: 0.06362942, z: 0.041950554, w: 0.996964}
  m_LocalPosition: {x: -0.035876416, y: 0.07426513, z: 0.0054220953}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2749870845763744358}
  m_Father: {fileID: 7577900487402064988}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &6606507566438310603
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2027895492383954671}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00854818, y: 0.0037305583, z: -0.00075503136, w: 0.99995625}
  m_LocalPosition: {x: 0.000000038817145, y: 0.023551904, z: -0.00000008150554}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 692624218676177310}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6610317430992866894
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4400452983571757765}
  m_Layer: 0
  m_Name: RightHandThumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6643006642981370824
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 675675067294835507}
  serializedVersion: 2
  m_LocalRotation: {x: 0.046991147, y: 0.022841858, z: 0.27797118, w: 0.9591674}
  m_LocalPosition: {x: 0.000000019308526, y: 0.028347531, z: 0.00000027363967}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6798434318218008223}
  m_Father: {fileID: 3155363803605568173}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6657430115615426907
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4390012197431013410}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c2fadf230d1919748a9aa21d40f74619, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TrackingType: 0
  m_UpdateType: 0
  m_IgnoreTrackingState: 0
  m_PositionInput:
    m_UseReference: 1
    m_Action:
      m_Name: Position
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: 0bacfa51-7938-4a88-adae-9e8ba6c59d23
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings:
      - m_Name: 
        m_Id: f5efb008-b167-4d0f-b9e0-49a2350a85b3
        m_Path: <XRHMD>/centerEyePosition
        m_Interactions: 
        m_Processors: 
        m_Groups: 
        m_Action: Position
        m_Flags: 0
      m_Flags: 0
    m_Reference: {fileID: 7862207684358717888, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_RotationInput:
    m_UseReference: 1
    m_Action:
      m_Name: Rotation
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: 5439f14e-c9da-4bd1-ad3f-7121a75c10d9
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings:
      - m_Name: 
        m_Id: f984a7fd-f7e2-45ef-b21d-699a5d160f29
        m_Path: <XRHMD>/centerEyeRotation
        m_Interactions: 
        m_Processors: 
        m_Groups: 
        m_Action: Rotation
        m_Flags: 0
      m_Flags: 0
    m_Reference: {fileID: -530380113134220495, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_TrackingStateInput:
    m_UseReference: 1
    m_Action:
      m_Name: Tracking State Input
      m_Type: 0
      m_ExpectedControlType: Integer
      m_Id: be9cc21d-5595-4ea6-aa72-e48652a11968
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 1031966339891076899, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
  m_PositionAction:
    m_Name: Position
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 0bacfa51-7938-4a88-adae-9e8ba6c59d23
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings:
    - m_Name: 
      m_Id: f5efb008-b167-4d0f-b9e0-49a2350a85b3
      m_Path: <XRHMD>/centerEyePosition
      m_Interactions: 
      m_Processors: 
      m_Groups: 
      m_Action: Position
      m_Flags: 0
    m_Flags: 0
  m_RotationAction:
    m_Name: Rotation
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 5439f14e-c9da-4bd1-ad3f-7121a75c10d9
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings:
    - m_Name: 
      m_Id: f984a7fd-f7e2-45ef-b21d-699a5d160f29
      m_Path: <XRHMD>/centerEyeRotation
      m_Interactions: 
      m_Processors: 
      m_Groups: 
      m_Action: Rotation
      m_Flags: 0
    m_Flags: 0
--- !u!33 &6666113864808383841
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6117099419200726635}
  m_Mesh: {fileID: -1120971793077124694, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!143 &6668217725418410777
CharacterController:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 3
  m_Height: 1.85
  m_Radius: 0.28
  m_SlopeLimit: 45
  m_StepOffset: 0.25
  m_SkinWidth: 0.02
  m_MinMoveDistance: 0
  m_Center: {x: 0, y: 0.02, z: 0}
--- !u!114 &6673519039911710674
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6090362364418762880}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d6bcfb2733274139b46209d57bc3d44, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  playerCharacterRoot: {fileID: 4285635575700484274}
  rig: {fileID: 8904514290230835218}
  rigBuilder: {fileID: 3354135046105739863}
  rigBuilderPlayer: {fileID: 0}
  playerRootAnimator: {fileID: 5726604882724289091}
  PlayerAnimator: {fileID: 0}
  rightHandIK: {fileID: 431838966104137747}
  leftHandIK: {fileID: 988006580706884662}
  headIK: {fileID: 6567829091087403004}
  rightLegIK: {fileID: 9220416919784355882}
  leftLegIK: {fileID: 6728438529743207398}
  playerAimationClip: {fileID: 0}
  rightHandTipName: RightHand
  rightHandMidName: RightForeArm
  rightHandRootName: RightArm
  leftHandTipName: LeftHand
  leftHandMidName: LeftForeArm
  leftHandRootName: LeftArm
  headName: Head
  rightLegTipName: RightFoot
  rightLegMidName: RightLeg
  rightLegRootName: RightUpLeg
  leftLegTipName: LeftFoot
  leftLegMidName: LeftLeg
  leftLegRootName: LeftUpLeg
  vuplexWebViewObject: {fileID: 0}
  vR_Rig_Manager: {fileID: 0}
  chipSelectionManager: {fileID: 0}
--- !u!1 &6705022225288302881
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6141893153934801516}
  - component: {fileID: 810874885818669664}
  - component: {fileID: 6230530511651330972}
  m_Layer: 0
  m_Name: Button_B
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6706157922200873912
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 433312847804040521}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7337106021779240675}
  - {fileID: 6327557944332572447}
  - {fileID: 5937777216826553390}
  - {fileID: 3539810304606826006}
  - {fileID: 7557062431449280220}
  m_Father: {fileID: 496353250390962969}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6728438529743207398
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5553491692786752903}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aeda7bfbf984f2a4da5ab4b8967b115d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Weight: 1
  m_Data:
    m_Root: {fileID: 2333636119849558983}
    m_Mid: {fileID: 5061350368787562714}
    m_Tip: {fileID: 332667422860186626}
    m_Target: {fileID: 1959705464501603054}
    m_Hint: {fileID: 1101418946447317244}
    m_TargetPositionWeight: 1
    m_TargetRotationWeight: 1
    m_HintWeight: 1
    m_MaintainTargetPositionOffset: 0
    m_MaintainTargetRotationOffset: 0
--- !u!33 &6739654394357090568
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4268915084993462964}
  m_Mesh: {fileID: -8653722315008560443, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!1 &6743486549747732338
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3984814576672651556}
  - component: {fileID: 4338852539094006615}
  m_Layer: 0
  m_Name: XR Interaction Manager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6790821682542554873
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8324876636362646592}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000013985816, y: -0.1305262, z: 0.000000018412676, w: 0.9914449}
  m_LocalPosition: {x: -0.012636564, y: -0.028556997, z: 0.027326612}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2072851329618579022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &6798434318218008223
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 553272870018331482}
  serializedVersion: 2
  m_LocalRotation: {x: -0.028064048, y: -0.004269748, z: -0.016245605, w: 0.999465}
  m_LocalPosition: {x: 0.0000000096423864, y: 0.025792072, z: -0.00000016442598}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6643006642981370824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &6829439297224888606
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7614375382012231164}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -6.952414e-10, y: -0.012954317, z: -0.020195028}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2072851329618579022}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!224 &6854304056576790987
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1766749749359418194}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1979724344501094098}
  - {fileID: 3086063312870280375}
  - {fileID: 2341674191280902196}
  m_Father: {fileID: 7095667580457311747}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -33.5}
  m_SizeDelta: {x: 160, y: 20}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &6868920332256386382
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6338013453508777102}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00012788874, y: -0.000000093437755, z: 1.194087e-11, w: 1}
  m_LocalPosition: {x: 0, y: 1.640622, z: -0.025199234}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4514296614285512320}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6915321513614555395
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6137808655463954828}
  - component: {fileID: 611654447958032654}
  - component: {fileID: 261570902888713761}
  m_Layer: 0
  m_Name: Fill
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &6915648343438824661
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 75b29b6c6428c984a8a73ffc2d58063b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 5916155045363674533}
  m_TransformationPriority: 0
  m_TurnSpeed: 50
  m_LeftHandTurnInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Left Hand Turn
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 93501666-9f71-46d0-8067-36084217a33d
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 0}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_RightHandTurnInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Right Hand Turn
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 17fb6a83-5302-4afa-b0d6-66af1e01cd88
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -6493913391331992944, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
--- !u!1 &6930141933070963737
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3421309572655730664}
  m_Layer: 0
  m_Name: Spine2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &6931096129579237216
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4545920090398020782}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c83f12c537584f51b92c01f10d7090c0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_TransitionDuration: 0.125
  m_InteractorSource: {fileID: 6973807910609024344}
  m_IgnoreHoverEvents: 0
  m_IgnoreSelectEvents: 0
  m_IgnoreActivateEvents: 1
  m_IgnoreUGUIHover: 0
  m_IgnoreUGUISelect: 0
  m_IgnoreXRInteractionEvents: 0
  m_SelectClickAnimationMode: 1
  m_ActivateClickAnimationMode: 1
  m_ClickAnimationDuration: 0.25
  m_ClickAnimationCurve:
    m_UseConstant: 1
    m_ConstantValue:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    m_Variable: {fileID: 0}
--- !u!1 &6966779063830867726
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6327557944332572447}
  - component: {fileID: 1062557283441490215}
  - component: {fileID: 1795399876645144408}
  - component: {fileID: 7648912439904453997}
  - component: {fileID: 1731497579831748185}
  - component: {fileID: 5828405131873502227}
  m_Layer: 0
  m_Name: Near-Far Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &6973807910609024344
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2088670065357337757}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0924bcaa9eb50df458a783ae0e2b59f5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 1
  m_Handedness: 2
  m_AttachTransform: {fileID: 5994736955204494484}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_PokeDepth: 0.1
  m_PokeWidth: 0.0075
  m_PokeSelectWidth: 0.015
  m_PokeHoverRadius: 0.015
  m_PokeInteractionOffset: 0.005
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_PhysicsTriggerInteraction: 1
  m_RequirePokeFilter: 1
  m_EnableUIInteraction: 1
  m_DebugVisualizationsEnabled: 0
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
--- !u!33 &6974596716163073188
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3381608978322424781}
  m_Mesh: {fileID: -2564423107879867638, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!1 &7003079085475866073
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3516606857722637648}
  m_Layer: 0
  m_Name: RightHandIndex3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &7004919098972313426
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7851588616201146884}
  - component: {fileID: 1436625747208450544}
  m_Layer: 0
  m_Name: AvaturnMobile
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &7025305998219498638
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4109662309346090169}
  m_Mesh: {fileID: -4189514412694937182, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!4 &7052516765373991646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6117099419200726635}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000008146034, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.0020741627, z: -0.0052528577}
  m_LocalScale: {x: 0.982392, y: 1.55, z: 0.982392}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 318914851436607795}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!224 &7062042876505683019
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8247737509939603634}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5703946418305378629}
  - {fileID: 3032641275601302522}
  - {fileID: 8760523660980945685}
  - {fileID: 679166823348159564}
  - {fileID: 2609061317010167862}
  - {fileID: 6097894791315742696}
  - {fileID: 4919935681502709809}
  - {fileID: 2207550626579633132}
  m_Father: {fileID: 7851588616201146884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!1 &7082462151077418800
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 118680253955205976}
  m_Layer: 0
  m_Name: Left_Hand_IK_target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!137 &7090847028827831956
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4004642021144670011}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -1478458054894493653, guid: c2ca2d7a8cc7e344285ab81de5769515, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -3209228934493779274, guid: c2ca2d7a8cc7e344285ab81de5769515, type: 3}
  m_Bones:
  - {fileID: 4975108577388796292}
  - {fileID: 7271366320287254483}
  - {fileID: 8194709199298547771}
  - {fileID: 3421309572655730664}
  - {fileID: 559369952382032964}
  - {fileID: 6223613710194887899}
  - {fileID: 4391677642099535108}
  - {fileID: 1228367496959184203}
  - {fileID: 715742585284539564}
  - {fileID: 7577900487402064988}
  - {fileID: 3155363803605568173}
  - {fileID: 6643006642981370824}
  - {fileID: 6798434318218008223}
  - {fileID: 4386289235407016483}
  - {fileID: 634068311700983852}
  - {fileID: 2808095484452820845}
  - {fileID: 7421115535572327446}
  - {fileID: 692624218676177310}
  - {fileID: 6606507566438310603}
  - {fileID: 5666268692752496191}
  - {fileID: 7322571774275400544}
  - {fileID: 3878823456568567506}
  - {fileID: 6596787623609719101}
  - {fileID: 2749870845763744358}
  - {fileID: 6259267943173207598}
  - {fileID: 8978555451093852455}
  - {fileID: 7334807809387987209}
  - {fileID: 8517523847440643472}
  - {fileID: 279653332813315513}
  - {fileID: 4400452983571757765}
  - {fileID: 4380871466541375476}
  - {fileID: 2947483358204636977}
  - {fileID: 5777818085006005823}
  - {fileID: 1286356308969779788}
  - {fileID: 3516606857722637648}
  - {fileID: 5911338096449038408}
  - {fileID: 3447113454583995759}
  - {fileID: 7161254564539608682}
  - {fileID: 1460506307876685992}
  - {fileID: 2788367612727919741}
  - {fileID: 1089412022518228957}
  - {fileID: 5222654588366059119}
  - {fileID: 6110409109699714452}
  - {fileID: 5664199988977456258}
  - {fileID: 2333636119849558983}
  - {fileID: 5061350368787562714}
  - {fileID: 332667422860186626}
  - {fileID: 8974198899552680212}
  - {fileID: 883269954257450315}
  - {fileID: 8023833221090823321}
  - {fileID: 9000211819660584494}
  - {fileID: 1294047678815427870}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4975108577388796292}
  m_AABB:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_DirtyAABB: 0
--- !u!114 &7095218367804858521
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6123179565880450011}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1410cbaaadf84a7aaa6459d37ad21b3a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Renderer: {fileID: 2234876244449394634}
  m_MaterialIndex: 0
--- !u!224 &7095667580457311747
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3731247915614967799}
  m_LocalRotation: {x: 0.4424829, y: -0, z: -0, w: 0.896777}
  m_LocalPosition: {x: 0, y: 0, z: 1.6}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4409373711675685735}
  - {fileID: 6854304056576790987}
  - {fileID: 119037776445706993}
  m_Father: {fileID: 4285635575700484274}
  m_LocalEulerAnglesHint: {x: 52.525, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -0.0148, y: 1.01}
  m_SizeDelta: {x: 211.8059, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &7104329100137566756
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2341254861155593623}
  serializedVersion: 2
  m_LocalRotation: {x: 0.009321937, y: -0.512114, z: -0.8588522, w: 0.005028115}
  m_LocalPosition: {x: 0.085706234, y: 0.09145445, z: -0.011317253}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3993044746207940765}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7140555829586447331
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7559781762829685010}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &7141565287705451588
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 279653332813315513}
  m_Layer: 0
  m_Name: RightHand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7161254564539608682
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6367351471831352310}
  serializedVersion: 2
  m_LocalRotation: {x: 0.008548177, y: -0.0037305611, z: 0.0007550308, w: 0.99995625}
  m_LocalPosition: {x: -0.000000039175788, y: 0.023551917, z: -0.000000023962206}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3447113454583995759}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7196990757102031841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 332667422860186626}
  m_Layer: 0
  m_Name: LeftFoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &7238856827040566940
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8247737509939603634}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6cdb08602b68f894e9325e537ea86fff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _isOpen: 0
  _frameController: {fileID: 1436625747208450544}
  _openEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7559781762829685010}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 1441413611533425565}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
  _closeEvent:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7559781762829685010}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 1441413611533425565}
        m_TargetAssemblyTypeName: UnityEngine.GameObject, UnityEngine
        m_MethodName: SetActive
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!222 &7244796730452608866
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 71643286533970796}
  m_CullTransparentMesh: 1
--- !u!1 &7245424946418628372
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5222654588366059119}
  m_Layer: 0
  m_Name: RightHandPinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!222 &7255750962920319719
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 966853248386523949}
  m_CullTransparentMesh: 1
--- !u!4 &7265973089823045129
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169109927468303437}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6411585347646833231}
  m_Father: {fileID: 545667201402663227}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &7271366320287254483
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 717773650996758387}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0096168835, y: -0.000000047890897, z: -0.0000000019212236,
    w: 0.99995375}
  m_LocalPosition: {x: -3.8081794e-16, y: 0.09577868, z: 0.0000000011628201}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8194709199298547771}
  m_Father: {fileID: 4975108577388796292}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7286427651859038901
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9031794777427850043}
  - component: {fileID: 541500167590247934}
  - component: {fileID: 4574441567135131192}
  - component: {fileID: 143614111516700755}
  m_Layer: 0
  m_Name: Gaze Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &7322571774275400544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2260743472254961591}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0005930515, y: -0.008879056, z: 0.00084161427, w: 0.99996006}
  m_LocalPosition: {x: 0.0000000018961428, y: 0.03439283, z: 0.0000002498251}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3878823456568567506}
  m_Father: {fileID: 5666268692752496191}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &7334807809387987209
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1062036589615467262}
  serializedVersion: 2
  m_LocalRotation: {x: -0.004415341, y: 0.0839764, z: -0.027910043, w: 0.996067}
  m_LocalPosition: {x: 0.000000046182898, y: 0.1496083, z: 0.0000003251908}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8517523847440643472}
  m_Father: {fileID: 8978555451093852455}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &7337106021779240675
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9205554656255805001}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4452282283316580681}
  - {fileID: 4683310073714609297}
  m_Father: {fileID: 6706157922200873912}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &7348354011957679096
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4186497871359179949}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &7350190245303620506
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 318914851436607795}
  m_Layer: 0
  m_Name: UniversalController
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &7373554196297813687
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2833875080643800683}
  m_Mesh: {fileID: -1120971793077124694, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!222 &7391560311073942447
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4432447139325270252}
  m_CullTransparentMesh: 1
--- !u!4 &7421115535572327446
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8346863236969677947}
  serializedVersion: 2
  m_LocalRotation: {x: 0.014933219, y: 0.030035647, z: -0.012575402, w: 0.9993582}
  m_LocalPosition: {x: -0.00026210616, y: 0.08388787, z: -0.000084393985}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 692624218676177310}
  m_Father: {fileID: 7577900487402064988}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7425416499237917644
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4975108577388796292}
  m_Layer: 0
  m_Name: Hips
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7428536110082899187
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3564190040523313381}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.444777, y: 1.5764611, z: -0.5453853}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7992904332792918524}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7437920921242551290
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2510119848456018298}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8b94c4c83dec6a94fbaebf543478259e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 7739599882840324738}
  m_TransformationPriority: 0
  m_EnableFreeXMovement: 1
  m_EnableFreeYMovement: 0
  m_EnableFreeZMovement: 1
  m_UseGravity: 1
  m_GravityApplicationMode: 0
  m_ControllerTransform: {fileID: 545667201402663227}
  m_EnableMoveWhileSelecting: 0
  m_MoveFactor: 1
  m_GrabMoveInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Grab Move
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 67220c99-f046-4e98-aa6f-d84114cad173
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Grab Move Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: ed114d26-3fbf-41fc-80fa-9675240038c5
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 15759602096507913, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: 15759602096507913, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_GrabMoveAction:
    m_UseReference: 1
    m_Action:
      m_Name: Grab Move
      m_Type: 0
      m_ExpectedControlType: 
      m_Id: de56d195-bf90-4347-9982-6bf8ffa3420c
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_Reference: {fileID: 0}
--- !u!1 &7461747219998478995
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7992904332792918524}
  - component: {fileID: 988006580706884662}
  m_Layer: 0
  m_Name: Left_Hand_IK
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &7520960489843355295
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5807584919204290509}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e3c5f6c9defa4ae9ad41bcc3f8754f86, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 4294967295
  m_Handedness: 0
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_ClimbProvider: {fileID: 4879764860342710102}
  m_DestinationEvaluationSettings:
    m_UseConstant: 1
    m_ConstantValue:
      m_EnableDestinationEvaluationDelay: 0
      m_DestinationEvaluationDelayTime: 1
      m_PollForDestinationChange: 1
      m_DestinationPollFrequency: 1
      m_DestinationFilterObject: {fileID: 11400000, guid: 0f906c94e2aa0c3488832acc1db04295,
        type: 2}
    m_Variable: {fileID: 0}
--- !u!4 &7557062431449280220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8737642189024693810}
  serializedVersion: 2
  m_LocalRotation: {x: 0.53567934, y: 0.46157086, z: 0.53567934, w: 0.46157086}
  m_LocalPosition: {x: -0.046, y: -0.032, z: -0.112}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6706157922200873912}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 98.5}
--- !u!1 &7559781762829685010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 679166823348159564}
  - component: {fileID: 8332856227757906472}
  - component: {fileID: 7140555829586447331}
  - component: {fileID: 9101641844593252138}
  m_Layer: 5
  m_Name: HideButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &7564145236229543277
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 848228539067598214}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2673107099597323512}
  m_Father: {fileID: 7851588616201146884}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &7577900487402064988
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2041486599090187651}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0010705733, y: 0.0007987697, z: 0.010620059, w: 0.9999427}
  m_LocalPosition: {x: -0.000000010120069, y: 0.25669506, z: -0.00000011861171}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4386289235407016483}
  - {fileID: 7421115535572327446}
  - {fileID: 6596787623609719101}
  - {fileID: 5666268692752496191}
  - {fileID: 3155363803605568173}
  - {fileID: 5849612435864072034}
  m_Father: {fileID: 715742585284539564}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7580238669463855221
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1460506307876685992}
  m_Layer: 0
  m_Name: RightHandRing1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &7583172574258901675
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9033221340210552336}
  m_Layer: 0
  m_Name: Right_Hand_VR_Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &7587747077337991569
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6536985365452920497}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f86d13fca2ec430d870c0f7765ad0dde, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_AffordanceStateProvider: {fileID: 5111301790308015182}
  m_ReplaceIdleStateValueWithInitialValue: 0
  m_AffordanceThemeDatum:
    m_UseConstant: 0
    m_ConstantValue:
      m_StateAnimationCurve:
        m_UseConstant: 1
        m_ConstantValue:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 0
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0
            outWeight: 0
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        m_Variable: {fileID: 0}
      m_List:
      - stateName: disabled
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: idle
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hovered
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: hoveredPriority
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: selected
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: activated
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      - stateName: focused
        animationStateStartValue: {r: 0, g: 0, b: 0, a: 0}
        animationStateEndValue: {r: 0, g: 0, b: 0, a: 0}
      m_ColorBlendMode: 0
      m_BlendAmount: 1
    m_Variable: {fileID: 11400000, guid: fc690d1505c48cb4696838b71abd2ca0, type: 2}
  m_ValueUpdated:
    m_PersistentCalls:
      m_Calls: []
  m_MaterialPropertyBlockHelper: {fileID: 576178702574633366}
  m_ColorPropertyName: _BaseColor
--- !u!1 &7605991476053792939
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2238439274622436440}
  m_Layer: 0
  m_Name: Right_Hand_IK_hint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &7614375382012231164
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6829439297224888606}
  - component: {fileID: 2073727678213263734}
  - component: {fileID: 350673018438696253}
  m_Layer: 0
  m_Name: Trigger
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &7648912439904453997
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6966779063830867726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 48139a683d3b4ac3a37cd5d24f71acf1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_CastOrigin: {fileID: 6327557944332572447}
  m_EnableStabilization: 0
  m_PositionStabilization: 0.25
  m_AngleStabilization: 20
  m_AimTargetObject: {fileID: 0}
  m_PhysicsLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_PhysicsTriggerInteraction: 1
  m_CastRadius: 0.1
--- !u!114 &7656257333935239958
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4432447139325270252}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5494d9d7c39544539adab71793f4a16a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &7665765232817965898
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8443053389285184463}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd0b9921bce4eeb49bd05815b1135ac2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractorSourceObject: {fileID: 4509087775236993182}
  m_HapticImpulsePlayer: {fileID: 6393680207812758106}
  m_PlaySelectEntered: 1
  m_SelectEnteredData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectExited: 0
  m_SelectExitedData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectCanceled: 0
  m_SelectCanceledData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverEntered: 1
  m_HoverEnteredData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverExited: 0
  m_HoverExitedData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverCanceled: 0
  m_HoverCanceledData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_AllowHoverHapticsWhileSelecting: 1
--- !u!33 &7698002628919082017
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4829954178147579237}
  m_Mesh: {fileID: 8449303727733987256, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!1 &7722944017648515500
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5347058672833980954}
  - component: {fileID: 431838966104137747}
  m_Layer: 0
  m_Name: Right_Hand_IK
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7730492227343333247
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600680041059445812}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 293992037062236184}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &7739599882840324738
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2620207173407796051}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6fa7b4195685c3846be746c74f0ab2f8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &7740014364801746234
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7843615889005886009}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!1 &7778125754123030126
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5099228242498882035}
  - component: {fileID: 4620685079338242643}
  - component: {fileID: 3813226075777540303}
  - component: {fileID: 6338921064379881376}
  m_Layer: 0
  m_Name: Screen Transition
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7780604994006172390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4186497871359179949}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000000059604645, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.008775876, y: -0.002558912, z: -0.0074315914}
  m_LocalScale: {x: 1.342947, y: 1.342947, z: 1.342947}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1508358508567715262}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7781576453842849366
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6a26c941eb8a46f7b6d00416227ab8c0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_XROrigin: {fileID: 8171373516390849214}
  m_BodyPositionEvaluatorObject: {fileID: 0}
  m_ConstrainedBodyManipulatorObject: {fileID: 0}
  m_UseCharacterControllerIfExists: 1
--- !u!114 &7797587527884744427
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1589479454740688656}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 64d299502104b064388841ec2adf6def, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Target: {fileID: 4452282283316580681}
  m_AimTargetObject: {fileID: 1062335248880780195}
  m_UseLocalSpace: 0
  m_AngleStabilization: 20
  m_PositionStabilization: 0.25
--- !u!114 &7812155368381269535
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169109927468303437}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 25a07ef133a37d140a87cdf1f1c75fdf, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractionManager: {fileID: 0}
  m_InteractionLayers:
    m_Bits: 1
  m_Handedness: 2
  m_AttachTransform: {fileID: 0}
  m_KeepSelectedTargetValid: 1
  m_DisableVisualsWhenBlockedInGroup: 1
  m_StartingSelectedInteractable: {fileID: 0}
  m_StartingTargetFilter: {fileID: 0}
  m_HoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_HoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_SelectEntered:
    m_PersistentCalls:
      m_Calls: []
  m_SelectExited:
    m_PersistentCalls:
      m_Calls: []
  m_StartingHoverFilters: []
  m_StartingSelectFilters: []
  m_SelectInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Select
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 2491b664-3d4e-4f20-a7ae-ee1861d845f2
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Select Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: fc42dacc-33eb-41ec-9c17-d242ac6b0c5b
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 187161793506945269, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: -1758520528963094988, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_ActivateInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: Activate
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: 77660b9e-6bbe-4740-b80f-1fea8d0f59e1
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: Activate Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 94568f52-c27a-47fc-a190-5e3b17572929
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 83097790271614945, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: 7904272356298805229, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_SelectActionTrigger: 1
  m_AllowHoveredActivate: 0
  m_TargetPriorityMode: 0
  m_HideControllerOnSelect: 0
  m_InputCompatibilityMode: 0
  m_PlayAudioClipOnSelectEntered: 0
  m_AudioClipForOnSelectEntered: {fileID: 0}
  m_PlayAudioClipOnSelectExited: 0
  m_AudioClipForOnSelectExited: {fileID: 0}
  m_PlayAudioClipOnSelectCanceled: 0
  m_AudioClipForOnSelectCanceled: {fileID: 0}
  m_PlayAudioClipOnHoverEntered: 0
  m_AudioClipForOnHoverEntered: {fileID: 0}
  m_PlayAudioClipOnHoverExited: 0
  m_AudioClipForOnHoverExited: {fileID: 0}
  m_PlayAudioClipOnHoverCanceled: 0
  m_AudioClipForOnHoverCanceled: {fileID: 0}
  m_AllowHoverAudioWhileSelecting: 1
  m_PlayHapticsOnSelectEntered: 0
  m_HapticSelectEnterIntensity: 0
  m_HapticSelectEnterDuration: 0
  m_PlayHapticsOnSelectExited: 0
  m_HapticSelectExitIntensity: 0
  m_HapticSelectExitDuration: 0
  m_PlayHapticsOnSelectCanceled: 0
  m_HapticSelectCancelIntensity: 0
  m_HapticSelectCancelDuration: 0
  m_PlayHapticsOnHoverEntered: 0
  m_HapticHoverEnterIntensity: 0
  m_HapticHoverEnterDuration: 0
  m_PlayHapticsOnHoverExited: 0
  m_HapticHoverExitIntensity: 0
  m_HapticHoverExitDuration: 0
  m_PlayHapticsOnHoverCanceled: 0
  m_HapticHoverCancelIntensity: 0
  m_HapticHoverCancelDuration: 0
  m_AllowHoverHapticsWhileSelecting: 1
  m_InteractionAttachController: {fileID: 260707104338999143}
  m_EnableNearCasting: 1
  m_NearInteractionCaster: {fileID: 2339269481727074501}
  m_NearCasterSortingStrategy: 1
  m_SortNearTargetsAfterTargetFilter: 0
  m_EnableFarCasting: 1
  m_FarInteractionCaster: {fileID: 1798731436442317273}
  m_FarAttachMode: 1
  m_EnableUIInteraction: 1
  m_BlockUIOnInteractableSelection: 1
  m_UIHoverEntered:
    m_PersistentCalls:
      m_Calls: []
  m_UIHoverExited:
    m_PersistentCalls:
      m_Calls: []
  m_UIPressInput:
    m_InputSourceMode: 2
    m_InputActionPerformed:
      m_Name: UI Press
      m_Type: 1
      m_ExpectedControlType: 
      m_Id: ded1ccb2-ff18-46c7-ade9-b80985fe2825
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionValue:
      m_Name: UI Press Value
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: d762660e-30e0-4a4d-8e2a-e6b553e03f11
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReferencePerformed: {fileID: 3279264004350380116, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_InputActionReferenceValue: {fileID: -5908353012961274365, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualPerformed: 0
    m_ManualValue: 0
    m_ManualQueuePerformed: 0
    m_ManualQueueWasPerformedThisFrame: 0
    m_ManualQueueWasCompletedThisFrame: 0
    m_ManualQueueValue: 0
    m_ManualQueueTargetFrame: 0
  m_UIScrollInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: UI Scroll
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: ef5cc4a5-b968-432c-9ae7-45e494178db0
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -6756787485274679044, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
--- !u!223 &7837770898695884418
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3731247915614967799}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &7843615889005886009
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 293992037062236184}
  - component: {fileID: 9014839235890293556}
  - component: {fileID: 7740014364801746234}
  - component: {fileID: 2310486626309057212}
  m_Layer: 0
  m_Name: Player_Info_Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7851588616201146884
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7004919098972313426}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -5.09, y: -0.08, z: -0.62}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 375204505576286140}
  - {fileID: 7564145236229543277}
  - {fileID: 7062042876505683019}
  - {fileID: 3506797319206247025}
  m_Father: {fileID: 4285635575700484274}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7852162772088412140
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 134444605805926517}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1410cbaaadf84a7aaa6459d37ad21b3a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Renderer: {fileID: 4278881197966947620}
  m_MaterialIndex: 0
--- !u!222 &7899463311402586077
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4691006411662421713}
  m_CullTransparentMesh: 1
--- !u!114 &7927393120081356902
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 26e54e5a728a9234ab24fcf1460ed8a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MoveSpeed: 3
  SprintSpeed: 8
  RotationSmoothTime: 0.12
  SpeedChangeRate: 10
  LandingAudioClip: {fileID: 0}
  FootstepAudioClips: []
  FootstepAudioVolume: 0.5
  JumpHeight: 1.2
  Gravity: 0
  JumpTimeout: 0.5
  FallTimeout: 0.15
  Grounded: 1
  GroundedOffset: -0.14
  GroundedRadius: 0.28
  GroundLayers:
    serializedVersion: 2
    m_Bits: 1
  CinemachineCameraTarget: {fileID: 4390012197431013410}
  TopClamp: 70
  BottomClamp: -30
  CameraAngleOverride: 0
  LockCameraPosition: 0
--- !u!1 &7941259143105206269
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4683310073714609297}
  m_Layer: 0
  m_Name: Poke Point Affordances
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7992904332792918524
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7461747219998478995}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 118680253955205976}
  - {fileID: 7428536110082899187}
  m_Father: {fileID: 3446222053752739363}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!20 &7992926882691089504
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4390012197431013410}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 2
  m_BackGroundColor: {r: 0, g: 0, b: 0, a: 1}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.01
  far clip plane: 1000
  field of view: 60
  orthographic: 0
  orthographic size: 5
  m_Depth: -1
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 1
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!114 &7995985868530763277
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01f69dc1cb084aa42b2f2f8cd87bc770, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Mediator: {fileID: 5916155045363674533}
  m_TransformationPriority: 0
  m_DelayTime: 0
--- !u!1 &8020169616977755655
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1294047678815427870}
  m_Layer: 0
  m_Name: RightToeBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8023833221090823321
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5882278563207976760}
  serializedVersion: 2
  m_LocalRotation: {x: -0.043960586, y: -0.016831433, z: 0.0007407517, w: 0.99889123}
  m_LocalPosition: {x: -0.0000000064336887, y: 0.41549367, z: -1.3942836e-10}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9000211819660584494}
  m_Father: {fileID: 883269954257450315}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8035523432992933076
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3853495253572125805}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e293aa5d445469f418e6e156894e5326, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &8075031838612634213
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1072080526943758045}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a4a50d88b55b45648927679791f472de, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_GroupName: Right
  m_InteractionManager: {fileID: 0}
  m_StartingGroupMembers:
  - {fileID: 6973807910609024344}
  - {fileID: 7812155368381269535}
  m_StartingInteractionOverridesMap:
  - groupMember: {fileID: 6973807910609024344}
    overrideGroupMembers:
    - {fileID: 7812155368381269535}
--- !u!114 &8083315982671937065
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4691006411662421713}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5494d9d7c39544539adab71793f4a16a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!222 &8110384688326307851
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6432746359520744335}
  m_CullTransparentMesh: 1
--- !u!1 &8112452301063554017
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3878823456568567506}
  m_Layer: 0
  m_Name: LeftHandRing3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &8142670312825056300
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3539810304606826006}
  - component: {fileID: 2401820348149325874}
  m_Layer: 0
  m_Name: Left Controller Visual
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!114 &8154339635827155344
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5610636252869755943}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d4da16e775cc846fcbb039be2000adb2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  headMap:
    vrTarget: {fileID: 6572348054715713375}
    rigTarget: {fileID: 6868920332256386382}
    trackingPosOffset: {x: -0.83, y: -0.62, z: -1}
    trackingRotOffset: {x: 0, y: 0, z: 0}
  rightHandMap:
    vrTarget: {fileID: 9033221340210552336}
    rigTarget: {fileID: 3806786343414362346}
    trackingPosOffset: {x: 0, y: 0, z: 0}
    trackingRotOffset: {x: 0, y: 0, z: 0}
  leftHandMap:
    vrTarget: {fileID: 7557062431449280220}
    rigTarget: {fileID: 118680253955205976}
    trackingPosOffset: {x: 0, y: 0, z: 0}
    trackingRotOffset: {x: 0, y: 0, z: 0}
  headIK_BodyPosOffset: {x: 0, y: -1.65, z: 0}
  headBodyYawOffset: 0
  canUpdate: 1
  turnSmoothnes: 0.1
  vR_IK_ConstraintsManager: {fileID: 6673519039911710674}
--- !u!222 &8162697028205064111
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1521370347279902152}
  m_CullTransparentMesh: 1
--- !u!4 &8169210416909721770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 484607128982752228}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6327557944332572447}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8171373516390849214
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e0cb9aa70a22847b5925ee5f067c10a9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Camera: {fileID: 7992926882691089504}
  m_OriginBaseGameObject: {fileID: 4671199710004930166}
  m_CameraFloorOffsetObject: {fileID: 1514165820928196898}
  m_RequestedTrackingOriginMode: 2
  m_CameraYOffset: 1.36144
--- !u!4 &8194709199298547771
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: ************8853448}
  serializedVersion: 2
  m_LocalRotation: {x: -0.061165947, y: -0.000000022125105, z: 0.000000016504753,
    w: 0.99812764}
  m_LocalPosition: {x: -4.3928624e-15, y: 0.11876404, z: -0.000000012258043}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3421309572655730664}
  m_Father: {fileID: 7271366320287254483}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8233017956366635696
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1101418946447317244}
  m_Layer: 0
  m_Name: Left_Leg_IK_hint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &8245089391778421998
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8324876636362646592}
  m_Mesh: {fileID: -4189514412694937182, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!1 &8247737509939603634
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7062042876505683019}
  - component: {fileID: 6355024907514659822}
  - component: {fileID: 4103289382005180883}
  - component: {fileID: 5414809316943312770}
  - component: {fileID: 5098067480823488016}
  - component: {fileID: 7238856827040566940}
  m_Layer: 5
  m_Name: UI (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!23 &8253591638352346841
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3203986923887316783}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &8268868808138030560
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1689585148759238803}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 6206877780613868119}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7238856827040566940}
        m_TargetAssemblyTypeName: Avaturn.MenuMobile, Avaturn.Core
        m_MethodName: Open
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!4 &8274526277103049002
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5986276381935346121}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2620991336296125861}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!210 &8278728292318932326
SortingGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 545711285861446478}
  m_Enabled: 1
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 30005
  m_SortAtRoot: 0
--- !u!1 &8288753712382547403
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 692624218676177310}
  m_Layer: 0
  m_Name: LeftHandMiddle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!1 &8324876636362646592
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6790821682542554873}
  - component: {fileID: 8245089391778421998}
  - component: {fileID: 6116528445589379180}
  m_Layer: 0
  m_Name: Bumper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!222 &8332856227757906472
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7559781762829685010}
  m_CullTransparentMesh: 0
--- !u!1 &8346863236969677947
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7421115535572327446}
  m_Layer: 0
  m_Name: LeftHandMiddle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &8375457624469957523
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3203986923887316783}
  m_Mesh: {fileID: 5083779560280695074, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!114 &8385989862410996468
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3652913999594940763}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e988983f96fe1dd48800bcdfc82f23e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LineWidth: 0.01
  m_OverrideInteractorLineLength: 0
  m_LineLength: 10
  m_AutoAdjustLineLength: 0
  m_MinLineLength: 0.02
  m_UseDistanceToHitAsMaxLineLength: 1
  m_LineRetractionDelay: 0.5
  m_LineLengthChangeSpeed: 12
  m_WidthCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  m_SetLineColorGradient: 1
  m_ValidColorGradient:
    serializedVersion: 2
    key0: {r: 0.1254902, g: 0.5882353, b: 0.9529412, a: 0.5882353}
    key1: {r: 0.1254902, g: 0.5882353, b: 0.9529412, a: 0.5882353}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_InvalidColorGradient:
    serializedVersion: 2
    key0: {r: 1, g: 0, b: 0, a: 0.5882353}
    key1: {r: 1, g: 0, b: 0, a: 0.5882353}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_BlockedColorGradient:
    serializedVersion: 2
    key0: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    key1: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_TreatSelectionAsValidState: 0
  m_SmoothMovement: 0
  m_FollowTightness: 10
  m_SnapThresholdDistance: 10
  m_Reticle: {fileID: 8748868027195207512, guid: 893219773891c784ab469a39151879b4,
    type: 3}
  m_BlockedReticle: {fileID: 3177232254315139758, guid: a3fde713df4d99042a0403c4be9eea32,
    type: 3}
  m_StopLineAtFirstRaycastHit: 1
  m_StopLineAtSelection: 0
  m_SnapEndpointIfAvailable: 1
  m_LineBendRatio: 0.5
  m_BendingEnabledInteractionLayers:
    m_Bits: 4294967295
  m_OverrideInteractorLineOrigin: 1
  m_LineOriginTransform: {fileID: 4452282283316580681}
  m_LineOriginOffset: 0
--- !u!114 &8399808866277470502
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3731247915614967799}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!114 &8413157986821949054
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6238845496079627304}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 14
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Select Avatar
--- !u!114 &8436963578917438688
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2557475164967223925}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 25
    m_FontStyle: 0
    m_BestFit: 1
    m_MinSize: 2
    m_MaxSize: 25
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Can't run demo in Editor. Select build platform "Android" or "iOS" and
    build project to test Avaturn on mobile.
--- !u!1 &8443053389285184463
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1437907489910130082}
  - component: {fileID: 4509087775236993182}
  - component: {fileID: 5072605204120713644}
  - component: {fileID: 9134061049148083454}
  - component: {fileID: 2540319103546819079}
  - component: {fileID: 7665765232817965898}
  m_Layer: 0
  m_Name: Teleport Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8473767348284954952
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2620207173407796051}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4751113485835488039}
  - {fileID: 3640138343546388506}
  - {fileID: 1727364723895473379}
  - {fileID: 3533296154301763878}
  - {fileID: 4613608991934880773}
  m_Father: {fileID: 3797029478118711239}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!222 &8502848787180089549
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3232823210907559783}
  m_CullTransparentMesh: 0
--- !u!33 &8503045284414179774
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1867588534553549969}
  m_Mesh: {fileID: 22788929071467060, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!4 &8506148699809443934
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5672919205124789578}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2620991336296125861}
  m_Father: {fileID: 4285635575700484274}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8514636028103646756
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2654499417665259012}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 03005cac3f6f875458946d66fc12fe82, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  chips: []
  _chipManager: {fileID: 0}
--- !u!4 &8517523847440643472
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5047964463645859763}
  serializedVersion: 2
  m_LocalRotation: {x: 0.004317509, y: -0.000350582, z: 0.037823796, w: 0.999275}
  m_LocalPosition: {x: 0.000000008973038, y: 0.2524032, z: -0.00000009427399}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 279653332813315513}
  m_Father: {fileID: 7334807809387987209}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &8526549258102202459
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4318703313496157382}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 99685157b02e4d446bbecb015645e5e8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8538758053982546435
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3806786343414362346}
  m_Layer: 0
  m_Name: Right_Hand_IK_target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!33 &8574657644153347715
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4349123533437619100}
  m_Mesh: {fileID: -2014588322676101042, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!33 &8608027681157781872
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5902537773636369907}
  m_Mesh: {fileID: 5083779560280695074, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!224 &8636585185768493816
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3232823210907559783}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 679166823348159564}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 38.381798}
  m_SizeDelta: {x: 0, y: 76.7616}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!33 &8691014723773380613
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044456232784710588}
  m_Mesh: {fileID: -8429650256770907399, guid: 147ae308eec018b40a7b312ae58f44c7, type: 3}
--- !u!4 &8695576789228953066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4390012197431013410}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6572348054715713375}
  - {fileID: 5099228242498882035}
  m_Father: {fileID: 496353250390962969}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8730272792476605426
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2061924075854312818}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4a5f76f9ea8c80547973ab01877f9567, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ThumbstickTransform: {fileID: 1765414289509432991}
  m_StickRotationRange: {x: 30, y: 30}
  m_StickInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Thumbstick
      m_Type: 0
      m_ExpectedControlType: Vector2
      m_Id: 8a80d2d1-54ab-40f3-ae58-acd679e9f63f
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -8666952849799569744, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: {x: 0, y: 0}
  m_TriggerTransform: {fileID: 1796092491595218133}
  m_TriggerXAxisRotationRange: {x: 0, y: -15}
  m_TriggerInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Trigger
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 594965ae-7ab6-4c16-889f-f371fa785459
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: 7904272356298805229, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
  m_GripTransform: {fileID: 3827930346453364790}
  m_GripRightRange: {x: -0.0125, y: -0.011}
  m_GripInput:
    m_InputSourceMode: 2
    m_InputAction:
      m_Name: Grip
      m_Type: 0
      m_ExpectedControlType: Axis
      m_Id: 615b1144-610d-48ff-a7ed-4f4447f2ed86
      m_Processors: 
      m_Interactions: 
      m_SingletonActionBindings: []
      m_Flags: 0
    m_InputActionReference: {fileID: -1758520528963094988, guid: c348712bda248c246b8c49b3db54643f,
      type: 3}
    m_ObjectReferenceObject: {fileID: 0}
    m_ManualValue: 0
--- !u!1 &8737642189024693810
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7557062431449280220}
  m_Layer: 0
  m_Name: Left_Hand_VR_Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &8760523660980945685
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1689585148759238803}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6510763906158283465}
  m_Father: {fileID: 7062042876505683019}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 1}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -286, y: -158}
  m_SizeDelta: {x: 500, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!4 &8761334942438416270
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2271816024791054615}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: -0.72}
  m_LocalScale: {x: 50, y: 50, z: 50}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5994736955204494484}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!114 &8801252366402239759
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2bf607ab212e64b058c9717b057fe684, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &8896671373857680896
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1169109927468303437}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd0b9921bce4eeb49bd05815b1135ac2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractorSourceObject: {fileID: 7812155368381269535}
  m_HapticImpulsePlayer: {fileID: 0}
  m_PlaySelectEntered: 1
  m_SelectEnteredData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectExited: 0
  m_SelectExitedData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectCanceled: 0
  m_SelectCanceledData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverEntered: 1
  m_HoverEnteredData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverExited: 0
  m_HoverExitedData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverCanceled: 0
  m_HoverCanceledData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_AllowHoverHapticsWhileSelecting: 0
--- !u!114 &8904514290230835218
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6090362364418762880}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70b342d8ce5c2fd48b8fa3147d48d1d1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Weight: 1
  m_Effectors:
  - m_Transform: {fileID: 3806786343414362346}
    m_Style:
      shape: {fileID: 4300000, guid: 687b70eb9c49243639f9379f6965034f, type: 2}
      color: {r: 1, g: 0, b: 0, a: 0.5}
      size: 0.1
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
    m_Visible: 1
  - m_Transform: {fileID: 2238439274622436440}
    m_Style:
      shape: {fileID: 4300000, guid: e050c2b16fe384bd994474655a4b4968, type: 2}
      color: {r: 1, g: 0, b: 0, a: 0.5}
      size: 0.1
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
    m_Visible: 1
  - m_Transform: {fileID: 118680253955205976}
    m_Style:
      shape: {fileID: 4300000, guid: 687b70eb9c49243639f9379f6965034f, type: 2}
      color: {r: 1, g: 0, b: 0, a: 0.5}
      size: 0.1
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
    m_Visible: 1
  - m_Transform: {fileID: 7428536110082899187}
    m_Style:
      shape: {fileID: 4300000, guid: e050c2b16fe384bd994474655a4b4968, type: 2}
      color: {r: 1, g: 0, b: 0, a: 0.5}
      size: 0.1
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
    m_Visible: 1
  - m_Transform: {fileID: 7104329100137566756}
    m_Style:
      shape: {fileID: 4300000, guid: 687b70eb9c49243639f9379f6965034f, type: 2}
      color: {r: 1, g: 0, b: 0, a: 0.5}
      size: 0.1
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
    m_Visible: 1
  - m_Transform: {fileID: 4158078768350670396}
    m_Style:
      shape: {fileID: 4300000, guid: e050c2b16fe384bd994474655a4b4968, type: 2}
      color: {r: 1, g: 0, b: 0, a: 0.5}
      size: 0.1
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
    m_Visible: 1
  - m_Transform: {fileID: 1959705464501603054}
    m_Style:
      shape: {fileID: 4300000, guid: 687b70eb9c49243639f9379f6965034f, type: 2}
      color: {r: 1, g: 0, b: 0, a: 0.5}
      size: 0.1
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
    m_Visible: 1
  - m_Transform: {fileID: 1101418946447317244}
    m_Style:
      shape: {fileID: 4300000, guid: e050c2b16fe384bd994474655a4b4968, type: 2}
      color: {r: 1, g: 0, b: 0, a: 0.5}
      size: 0.1
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0}
    m_Visible: 1
--- !u!114 &8923731902986500387
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 51e82e3dd30ec476d929290233311147, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  rightHandChipStackHolder: {fileID: 640092585440998972}
  leftHandChipStackHolder: {fileID: 8514636028103646756}
  rightHandSelectedChips: []
  leftHandSelectedChips: []
  rightHandLastSelectedChipIndex: 0
  leftHandLastSelectChipIndex: 0
  rightHandLastSelectedChipStack: {fileID: 0}
  leftHandLastSelectedChipStack: {fileID: 0}
  customBbtn: {fileID: 0}
  customYbtn: {fileID: 0}
  animator: {fileID: 0}
  moveInput: {fileID: 0}
--- !u!114 &8924248762193192422
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4916089409233462726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7951c64acb0fa62458bf30a60089fe2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 0
  m_CheckFor2DOcclusion: 0
  m_CheckFor3DOcclusion: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RaycastTriggerInteraction: 1
--- !u!114 &8928923035192546456
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9205554656255805001}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dd0b9921bce4eeb49bd05815b1135ac2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_InteractorSourceObject: {fileID: 1676166793883116007}
  m_HapticImpulsePlayer: {fileID: 1107543382018816462}
  m_PlaySelectEntered: 1
  m_SelectEnteredData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectExited: 0
  m_SelectExitedData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlaySelectCanceled: 0
  m_SelectCanceledData:
    m_Amplitude: 0.5
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverEntered: 1
  m_HoverEnteredData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverExited: 0
  m_HoverExitedData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_PlayHoverCanceled: 0
  m_HoverCanceledData:
    m_Amplitude: 0.25
    m_Duration: 0.1
    m_Frequency: 0
  m_AllowHoverHapticsWhileSelecting: 0
--- !u!224 &8943621866000577906
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4916089409233462726}
  m_LocalRotation: {x: 0.4424829, y: -0, z: -0, w: 0.896777}
  m_LocalPosition: {x: 0, y: 0, z: 1.6}
  m_LocalScale: {x: 0.01, y: 0.01, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3758277795233954449}
  m_Father: {fileID: 4285635575700484274}
  m_LocalEulerAnglesHint: {x: 52.525, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -0.0148, y: 1.01}
  m_SizeDelta: {x: 211.8059, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &8945399097114654071
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671199710004930166}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6a26c941eb8a46f7b6d00416227ab8c0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_XROrigin: {fileID: 8171373516390849214}
  m_BodyPositionEvaluatorObject: {fileID: 0}
  m_ConstrainedBodyManipulatorObject: {fileID: 0}
  m_UseCharacterControllerIfExists: 1
--- !u!4 &8974198899552680212
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4380271565080752509}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26037583, y: 0.0058337026, z: -0.0052681714, w: 0.9654753}
  m_LocalPosition: {x: -0.00000000813151, y: 0.15958963, z: -0.000000046913087}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 332667422860186626}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &8978555451093852455
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4278458331101851043}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5298264, y: 0.5298264, z: -0.46827766, w: 0.46827766}
  m_LocalPosition: {x: 0.052188158, y: 0.16465586, z: 0.009380956}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7334807809387987209}
  m_Father: {fileID: 3421309572655730664}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &9000211819660584494
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5184772924517249097}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5436202, y: 0.023267737, z: -0.0037890647, w: 0.83900017}
  m_LocalPosition: {x: 0.00000001507475, y: 0.43089125, z: 0.000000003532494}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1294047678815427870}
  m_Father: {fileID: 8023833221090823321}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!223 &9014839235890293556
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7843615889005886009}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!4 &9031794777427850043
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7286427651859038901}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 496353250390962969}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!4 &9033221340210552336
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7583172574258901675}
  serializedVersion: 2
  m_LocalRotation: {x: -0.53567934, y: 0.4615709, z: 0.53567934, w: -0.4615709}
  m_LocalPosition: {x: 0.046, y: -0.032, z: -0.112}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 545667201402663227}
  m_LocalEulerAnglesHint: {x: 0, y: 270, z: -98.5}
--- !u!114 &9101641844593252138
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7559781762829685010}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 7140555829586447331}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 7238856827040566940}
        m_TargetAssemblyTypeName: Avaturn.MenuMobile, Avaturn.Core
        m_MethodName: Close
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &9134061049148083454
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8443053389285184463}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e988983f96fe1dd48800bcdfc82f23e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_LineWidth: 0.01
  m_OverrideInteractorLineLength: 0
  m_LineLength: 10
  m_AutoAdjustLineLength: 0
  m_MinLineLength: 0.02
  m_UseDistanceToHitAsMaxLineLength: 1
  m_LineRetractionDelay: 0.5
  m_LineLengthChangeSpeed: 12
  m_WidthCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  m_SetLineColorGradient: 1
  m_ValidColorGradient:
    serializedVersion: 2
    key0: {r: 0.1254902, g: 0.5882353, b: 0.9529412, a: 0.5882353}
    key1: {r: 0.1254902, g: 0.5882353, b: 0.9529412, a: 0.5882353}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_InvalidColorGradient:
    serializedVersion: 2
    key0: {r: 1, g: 0, b: 0, a: 0.5882353}
    key1: {r: 1, g: 0, b: 0, a: 0.5882353}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_BlockedColorGradient:
    serializedVersion: 2
    key0: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    key1: {r: 1, g: 0.92156863, b: 0.015686275, a: 1}
    key2: {r: 0, g: 0, b: 0, a: 0}
    key3: {r: 0, g: 0, b: 0, a: 0}
    key4: {r: 0, g: 0, b: 0, a: 0}
    key5: {r: 0, g: 0, b: 0, a: 0}
    key6: {r: 0, g: 0, b: 0, a: 0}
    key7: {r: 0, g: 0, b: 0, a: 0}
    ctime0: 0
    ctime1: 65535
    ctime2: 0
    ctime3: 0
    ctime4: 0
    ctime5: 0
    ctime6: 0
    ctime7: 0
    atime0: 0
    atime1: 65535
    atime2: 0
    atime3: 0
    atime4: 0
    atime5: 0
    atime6: 0
    atime7: 0
    m_Mode: 0
    m_ColorSpace: -1
    m_NumColorKeys: 2
    m_NumAlphaKeys: 2
  m_TreatSelectionAsValidState: 0
  m_SmoothMovement: 0
  m_FollowTightness: 10
  m_SnapThresholdDistance: 10
  m_Reticle: {fileID: 8748868027195207512, guid: 893219773891c784ab469a39151879b4,
    type: 3}
  m_BlockedReticle: {fileID: 3177232254315139758, guid: a3fde713df4d99042a0403c4be9eea32,
    type: 3}
  m_StopLineAtFirstRaycastHit: 1
  m_StopLineAtSelection: 0
  m_SnapEndpointIfAvailable: 1
  m_LineBendRatio: 0.5
  m_BendingEnabledInteractionLayers:
    m_Bits: 4294967295
  m_OverrideInteractorLineOrigin: 1
  m_LineOriginTransform: {fileID: 5994736955204494484}
  m_LineOriginOffset: 0
--- !u!1 &9205554656255805001
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7337106021779240675}
  - component: {fileID: 1676166793883116007}
  - component: {fileID: 8928923035192546456}
  m_Layer: 0
  m_Name: Poke Interactor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &9220416919784355882
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2511277965148420841}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aeda7bfbf984f2a4da5ab4b8967b115d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Weight: 1
  m_Data:
    m_Root: {fileID: 883269954257450315}
    m_Mid: {fileID: 8023833221090823321}
    m_Tip: {fileID: 9000211819660584494}
    m_Target: {fileID: 7104329100137566756}
    m_Hint: {fileID: 4158078768350670396}
    m_TargetPositionWeight: 1
    m_TargetRotationWeight: 1
    m_HintWeight: 1
    m_MaintainTargetPositionOffset: 0
    m_MaintainTargetRotationOffset: 0
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 747373507}
  - {fileID: 128463936}
  - {fileID: 1010282838}
  - {fileID: 3797029478118711239}
  - {fileID: 807757972}
