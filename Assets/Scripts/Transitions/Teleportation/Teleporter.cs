using System.Collections;
using System.Collections.Generic;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Managing.Scened;
using FishNet.Object;
//using Scene_Teleportation_Kit.Scripts.teleport;
using UnityEngine;
//using UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets;
using static FishNet.Managing.Scened.UnloadOptions;
using UnityEngine.SceneManagement;
using StarterAssets;

public class Teleporter : NetworkBehaviour
{
    public string destinationScene;
    public string UnloaadScene;

    //public Scene destinationScene;
    public string destSpawnName;

    public Transform LasVegasLobbyObj;


    public GameObject ftueButtonPress;

    private void Start()
    {

    }

    // Method to check if the scene loading is complete
    //private void OnEnable()
    //{
    //    InstanceFinder.SceneManager.OnQueueEnd += OnSceneLoadComplete;
    //    InstanceFinder.SceneManager.OnLoadEnd += SceneManager_OnLoadEnd;
    //}

    //private void OnDisable()
    //{
    //    InstanceFinder.SceneManager.OnQueueEnd -= OnSceneLoadComplete;
    //    InstanceFinder.SceneManager.OnLoadEnd -= SceneManager_OnLoadEnd;
    //}
    //private void SceneManager_OnLoadEnd(SceneLoadEndEventArgs obj)
    //{
    //    canLoad = true;
    //}
    // Callback when the scene loading is complete
    private void OnSceneLoadComplete()
    {
        Debug.Log("Scene loading is complete!");
        // You can perform additional logic here after the scene has loaded
        canLoad = true;
    }



    void OnTriggerEnter(Collider collider)
    {
        Debug.Log("On Trigger Enter On Script Teleporter ");
        Debug.Log("On Trigger Enter::" + collider.name);
        //ThirdPersonController teleportable = collider.GetComponent<ThirdPersonController>();
        PlayerController teleportable = collider.GetComponent<PlayerController>();
        if (teleportable == null)
            return;
        if (destinationScene == "LASVEGAS_CITY" && MainManager.Instance.currentActvieScene() == "Lobby_Area")
        {
            if (!PlayerPrefs.HasKey("FTUE"))
            {
                DynamicPathGuide.instance.isPathGuideActive = false;
                ftueButtonPress.SetActive(true);
            }
            SceneTransitionManager.singleton.telportationTargetTemp = collider.GetComponent<NetworkObject>();

            VREnterBtn.SetActive(true);
        }
        else
        {
            //collider.GetComponent<PlayerController>().LoadingScreen.SetActive(true);

            // amazonq-ignore-next-line
            SceneTransitionManager.singleton.telportationTargetTemp = collider.GetComponent<NetworkObject>();
            Debug.Log("telportationTargetTemp::" + SceneTransitionManager.singleton.telportationTargetTemp);
            if (SceneTransitionManager.singleton.telportationTargetTemp != null)
            {
                Debug.Log("is Local Client::" + SceneTransitionManager.singleton.telportationTargetTemp.Owner.IsLocalClient);
                if (teleportable != null && collider.GetComponent<NetworkObject>().Owner.IsLocalClient)  //
                {
                    OnEnter(this.transform, true);//teleportable);
                }
            }
            //OnEnter(this.transform, true);//teleportable);
            
        }

    }
    [SerializeField] GameObject EnterButton;
    [SerializeField] GameObject VREnterBtn;

    private void OnTriggerStay(Collider other)
    {
        ThirdPersonController teleportable = other.GetComponent<ThirdPersonController>();
        if (teleportable != null && EnterButton != null)
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
            EnterButton.SetActive(true);
        }
    }

    private void OnTriggerExit(Collider other)
    {
        ThirdPersonController teleportable = other.GetComponent<ThirdPersonController>();
        if (teleportable != null && EnterButton != null)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            EnterButton.SetActive(true);
        }
        if (destinationScene == "LASVEGAS_CITY" && MainManager.Instance.currentActvieScene() == "Lobby_Area" && teleportable != null)
        {
            VREnterBtn.SetActive(false);
            if(!PlayerPrefs.HasKey("FTUE"))
            {
                ftueButtonPress.SetActive(false);
            }
        }
    }

    private void OnCollisionEnter(Collision collision)
    {
        // amazonq-ignore-next-line
        Debug.Log("OnCollisionEnter On Script Teleporter ");
        Debug.Log("OnCollisionEnter::" + collision.gameObject.name);
        PlayerController teleportable = collision.transform.GetComponent<PlayerController>();
        if (teleportable != null)
        {
            OnEnter(this.transform, true);//teleportable);   
        }
    }


    public void ClickTestFunction()
    {
        Debug.Log("Click Test Function Triggered");
    }

    [SerializeField] GameObject LoadingVFX;
    public void TeleportToWorld()
    {

        Debug.Log("TeleportToWorld Function Triggered");

        if (LoadingVFX != null)
            LoadingVFX.SetActive(true);
        if (LasVegasLobbyObj != null)
        {
            LasVegasLobbyObj.gameObject.SetActive(false);
        }
        OnEnter(this.transform, true);
        if (!PlayerPrefs.HasKey("FTUE"))
        {
            ftueButtonPress.SetActive(false);
            FTUEManager.instance.lasvegasDone();
        }
            
    }
    
[ServerRpc]
    public void OnEnter(Transform telportationTarget, bool isGetLocalPlayer)//Teleportable teleportable)
    {
        Debug.Log("OnEnter Function Tr   iggered || Teleportation Target ====>  " + telportationTarget + " || Is Get Local Player ====> " + isGetLocalPlayer);
        try
        {
            if (isGetLocalPlayer)
                telportationTarget = MainManager.Instance.localPlayerRigRoot.transform;
        }catch { }
        MainManager.Instance.LoadingScreen2D.SetActive(true);
        //telportationTarget.GetComponent<PlayerController>().LoadingScreen.SetActive(true);
        Debug.Log("Teleportation Target Object Name ====> " + telportationTarget.gameObject.name);

        //Debug.Log("Current Scene Name ====> " + SceneManager.GetActiveScene().name);
        Debug.Log("Destination Scene Name ====> " + destinationScene);


        if (MainManager.Instance.currentActvieScene().Equals(destinationScene))
        {
            telportationTarget.GetComponent<PlayerController>().LoadingScreen.SetActive(false);
            Teleport(telportationTarget);

            if (destSpawnName.Equals("ClassicCasnioXWardrobeSpawnPoint"))
            {
                // amazonq-ignore-next-line
                GameObject wardrobe = GameObject.Find("WardrobeManager");
                Debug.Log("Wardrobe name ====> " + wardrobe.name);

                WardrobeSceneManager wardrobeManager = wardrobe.GetComponent<WardrobeSceneManager>();
                PlayerController playerController = telportationTarget.GetComponent<PlayerController>();

                Debug.Log("wardrobeManager Name::" + wardrobeManager.name);
                Debug.Log("playerController name:::" + playerController.name);
                //if (!wardrobeManager)
                //    yield return null;

                //if (!playerController)
                //    yield return null;

                wardrobeManager.InitilializeAvaturnWebiview(playerController.avaturnIframeControllerMobile,
                    playerController.avatarReceiver,
                    playerController.vr_Rig_Manager,
                    playerController.mainPlayerCamera);
            }
        }
        else
        {
            StartCoroutine(TeleportToNewScene(destinationScene, telportationTarget)); //teleportable));
        }
    }

    bool canLoad = false;

    
    internal IEnumerator TeleportToNewScene(string sceneName, Transform telportationTarget)//Teleportable teleportable)
    {
        SceneTransitionManager.singleton.canLoadSceneData = false;


        //LoadScene(nob);   //Fishnet
        SceneTransitionManager.singleton.destSpawnName = destSpawnName;
        SceneTransitionManager.singleton.unloadScene = UnloaadScene;
        SceneTransitionManager.singleton.destinationScene = destinationScene;

        LoadScene(SceneTransitionManager.singleton.telportationTargetTemp, destinationScene);
        // amazonq-ignore-next-line
        /// Old Working Code ///
        //Scene currentScene = SceneManager.GetActiveScene();
        // SceneTransitionManager.singleton.GoToSceneAsync(destinationScene);
        // Debug.Log("Can Load Flag::" + SceneTransitionManager.singleton.canLoadSceneData);
        // while (!SceneTransitionManager.singleton.canLoadSceneData)
        // {
        //     yield return null;
        // }

        // Debug.Log("Can Load Flag::" + SceneTransitionManager.singleton.canLoadSceneData);

        // SceneManager.MoveGameObjectToScene(telportationTarget.gameObject, SceneManager.GetSceneByName(sceneName));
        // Teleport(telportationTarget);
        // telportationTarget.GetComponent<PlayerController>().LoadingScreen.SetActive(false);

        ///
        try
        {
            if (destinationScene.Equals("Wardrobe"))
            {
                // amazonq-ignore-next-line
                GameObject wardrobe = GameObject.Find("WardrobeManager");
                Debug.Log("Wardrobe name ====> " + wardrobe.name);

                WardrobeSceneManager wardrobeManager = wardrobe.GetComponent<WardrobeSceneManager>();
                PlayerController playerController = telportationTarget.GetComponent<PlayerController>();

                Debug.Log("wardrobeManager Name::" + wardrobeManager.name);
                Debug.Log("playerController name:::" + playerController.name);

                //if (!wardrobeManager)
                //    yield return null;

                //if (!playerController)
                //    yield return null;

                wardrobeManager.InitilializeAvaturnWebiview(playerController.avaturnIframeControllerMobile,
                playerController.avatarReceiver,
                playerController.vr_Rig_Manager,
                playerController.mainPlayerCamera);
            }
        }
        catch { }
        yield return null;
        //SceneManager.UnloadSceneAsync(currentScene);
        //MainManager.Instance.LoadingScreen2D.SetActive(false);
        Debug.Log("Unloading GeneralLobby");


        //UnloadScenes(telportationTarget.GetComponent<NetworkObject>(), UnloaadScene);
        //SceneUnloadData sud = new SceneUnloadData(UnloaadScene);

        //InstanceFinder.NetworkManager.SceneManager.UnloadConnectionScenes(sud);
    }

    private Dictionary<NetworkConnection, float> _triggeredTimes = new();
    
    [Server]
    private void LoadScene(NetworkObject triggeringIdentity, string scenename)
    {
        Debug.Log("InstanceFinder.NetworkManager.IsServerStarted::" + InstanceFinder.NetworkManager.IsServerStarted);
        // amazonq-ignore-next-line
        //    if (!InstanceFinder.NetworkManager.IsServerStarted)
        //        return;

        Debug.Log("triggeringIdentity::" + triggeringIdentity);
        //NetworkObject isn't necessarily needed but to ensure its the player only run if found.
        if (triggeringIdentity == null)
            return;


        /* Dont let trigger hit twice by same connection too frequently
         * See _triggeredTimes field for more info. */
        if (_triggeredTimes.TryGetValue(triggeringIdentity.Owner, out float time))
        {
            if (Time.time - time < 0.5f)
                return;
        }

        Debug.Log("LoadScene   >>>>>>");
        _triggeredTimes[triggeringIdentity.Owner] = Time.time;

        //Which objects to move.
        List<NetworkObject> movedObjects = new();
        movedObjects.Add(triggeringIdentity);

        //Load options.
        LoadOptions loadOptions = new()
        {
            AutomaticallyUnload = false,
        };

        //Make scene data.
        SceneLoadData sld = new(scenename);
        sld.PreferredActiveScene = new(sld.SceneLookupDatas[0]);
        sld.ReplaceScenes = ReplaceOption.None;
        sld.Options = loadOptions;
        sld.MovedNetworkObjects = movedObjects.ToArray();

        InstanceFinder.SceneManager.LoadConnectionScenes(triggeringIdentity.Owner, sld);
        Debug.Log("triggeringIdentity::" + triggeringIdentity);

    }

    //private void UnloadScenes(NetworkObject triggeringIdentity, string scenename)
    //{
    //    Debug.Log("InstanceFinder.NetworkManager.IsServerStarted::" + InstanceFinder.NetworkManager.IsServerStarted);
    //    if (!InstanceFinder.NetworkManager.IsServerStarted)
    //        return;

    //    //NetworkObject isn't necessarily needed but to ensure its the player only run if nob is found.
    //    if (triggeringIdentity == null)
    //        return;

    //    UnloadOptions unloadOptions = new()
    //    {
    //        Mode = (false) ? UnloadOptions.ServerUnloadMode.UnloadUnused : UnloadOptions.ServerUnloadMode.KeepUnused
    //    };

    //    SceneUnloadData sud = new(scenename);
    //    sud.Options = unloadOptions;
    //    Debug.Log("UnloadScenes::");
    //    InstanceFinder.SceneManager.UnloadConnectionScenes(triggeringIdentity.Owner, sud);
    //}

    public void Teleport(Transform telportationTarget)//Teleportable teleportable)
    {
        Debug.Log("telportationTarget::" + telportationTarget.name);
        Debug.Log("destSpawnName::" + destSpawnName);
        SpawnPoint spawnPoint = FindSpawnPoint(destSpawnName);
        if (spawnPoint != null)
        {
            Debug.Log("spawnPoint Found");
            telportationTarget.GetComponent<PlayerMovemetManager>().TeleportTo(spawnPoint.transform);
        }
        // teleportable.canTeleport = true;
    }

    private SpawnPoint FindSpawnPoint(string spawnName)
    {
        SpawnPoint[] spawnPoints = FindObjectsOfType<SpawnPoint>();
        foreach (SpawnPoint spawn in spawnPoints)
        {
            // amazonq-ignore-next-line
            SpawnPoint spawnPoint = spawn.GetComponent<SpawnPoint>();
            if (spawnPoint.spawnName == spawnName)
            {
                return spawnPoint;
            }
        }
        return null;
    }
}
